#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集群历史管理器
负责管理集群记录的持久化存储和通知逻辑
"""

import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any
import pytz
from core_logger import setup_logger

logger = setup_logger('cluster_history')

class ClusterHistoryManager:
    """集群历史管理器"""
    
    def __init__(self, history_file: str = "data/cluster_history.json"):
        self.history_file = history_file
        self.ensure_data_directory()
        self.history_data = self.load_history()
    
    def ensure_data_directory(self):
        """确保数据目录存在"""
        data_dir = os.path.dirname(self.history_file)
        if data_dir and not os.path.exists(data_dir):
            os.makedirs(data_dir)
            logger.info(f"创建数据目录: {data_dir}")
    
    def load_history(self) -> Dict[str, Any]:
        """加载历史记录"""
        if os.path.exists(self.history_file):
            try:
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                logger.info(f"加载历史记录: {len(data.get('clusters', []))} 个集群")
                return data
            except Exception as e:
                logger.error(f"加载历史记录失败: {e}")
                return self.create_empty_history()
        else:
            logger.info("历史记录文件不存在，创建新的记录")
            return self.create_empty_history()
    
    def create_empty_history(self) -> Dict[str, Any]:
        """创建空的历史记录结构"""
        return {
            "clusters": [],
            "last_updated": datetime.now(pytz.UTC).isoformat(),
            "version": "1.0"
        }
    
    def save_history(self):
        """保存历史记录到文件"""
        try:
            self.history_data["last_updated"] = datetime.now(pytz.UTC).isoformat()
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.history_data, f, ensure_ascii=False, indent=2, default=str)
            logger.debug("历史记录已保存")
        except Exception as e:
            logger.error(f"保存历史记录失败: {e}")
    
    def get_cluster_key(self, cluster: Dict[str, Any]) -> str:
        """生成集群的唯一标识 - 使用稳定的时间和事件特征"""
        start_time_str = cluster['start_time'].strftime('%Y-%m-%d %H:%M:%S')
        end_time_str = cluster['end_time'].strftime('%Y-%m-%d %H:%M:%S')
        event_count = cluster.get('event_count', 0)
        total_liquidations = int(cluster.get('total_liquidations', 0))

        # 使用时间范围 + 事件数量 + 总爆仓量作为唯一标识
        # 这样避免了cluster_id的不稳定性问题
        return f"{start_time_str}_{end_time_str}_{event_count}_{total_liquidations}"

    def is_cluster_stable(self, cluster: Dict[str, Any], stability_minutes: int = 1) -> bool:
        """判断集群是否稳定（结束时间距离当前时间超过指定分钟数）"""
        from datetime import datetime, timedelta
        import pytz

        current_time = datetime.now(pytz.UTC)
        cluster_end_time = cluster['end_time']

        # 如果集群时间没有时区信息，假设它是中国时区(UTC+8)
        if cluster_end_time.tzinfo is None:
            china_tz = pytz.timezone('Asia/Shanghai')
            cluster_end_time = china_tz.localize(cluster_end_time)

        # 转换为UTC时间进行比较
        cluster_end_time_utc = cluster_end_time.astimezone(pytz.UTC)
        time_since_end = current_time - cluster_end_time_utc
        is_stable = time_since_end >= timedelta(minutes=stability_minutes)

        # 转换为UTC+8显示
        china_tz = pytz.timezone('Asia/Shanghai')
        current_china = current_time.astimezone(china_tz).strftime('%Y-%m-%d %H:%M:%S')
        cluster_end_china = cluster_end_time_utc.astimezone(china_tz).strftime('%Y-%m-%d %H:%M:%S')
        logger.debug(f"稳定性检查: 当前时间UTC+8={current_china}, 集群结束时间UTC+8={cluster_end_china}, 距离现在={time_since_end}, 稳定={is_stable}")

        return is_stable
    
    def is_within_24_hours(self, cluster_time: datetime) -> bool:
        """判断集群时间是否在最近24小时内"""
        current_time = datetime.now(pytz.UTC)

        # 如果集群时间没有时区信息，假设它是中国时区(UTC+8)
        if cluster_time.tzinfo is None:
            china_tz = pytz.timezone('Asia/Shanghai')
            cluster_time = china_tz.localize(cluster_time)

        # 转换为UTC时间进行比较
        cluster_time_utc = cluster_time.astimezone(pytz.UTC)
        time_diff = current_time - cluster_time_utc

        is_within = time_diff <= timedelta(hours=24)
        # 转换为UTC+8显示
        china_tz = pytz.timezone('Asia/Shanghai')
        current_china = current_time.astimezone(china_tz).strftime('%Y-%m-%d %H:%M:%S')
        cluster_china = cluster_time_utc.astimezone(china_tz).strftime('%Y-%m-%d %H:%M:%S')
        logger.debug(f"24小时检查: 当前时间UTC+8={current_china}, 集群时间UTC+8={cluster_china}, 时间差={time_diff}, 结果={is_within}")

        return is_within
    
    def cluster_exists(self, cluster_key: str) -> bool:
        """检查集群是否已存在于历史记录中"""
        for cluster in self.history_data["clusters"]:
            if cluster["cluster_key"] == cluster_key:
                return True
        return False
    
    def add_cluster(self, cluster: Dict[str, Any], notified: bool = False, notification_sent: bool = False) -> bool:
        """添加新集群到历史记录

        Args:
            cluster: 集群数据
            notified: 是否标记为已通知状态（用于避免重复通知）
            notification_sent: 是否实际发送了通知（用于记录通知时间）
        """
        cluster_key = self.get_cluster_key(cluster)

        if self.cluster_exists(cluster_key):
            logger.debug(f"集群 {cluster_key} 已存在，跳过添加")
            return False

        # 创建集群记录
        cluster_record = {
            "cluster_key": cluster_key,
            "cluster_id": cluster.get('cluster_id'),
            "start_time": cluster['start_time'].isoformat(),
            "end_time": cluster['end_time'].isoformat(),
            "duration_seconds": cluster.get('duration_seconds'),
            "event_count": cluster.get('event_count'),
            "total_liquidations": cluster.get('total_liquidations'),
            "avg_liquidations": cluster.get('avg_liquidations'),
            "max_single_liquidation": cluster.get('max_single_liquidation'),
            "first_detected": datetime.now(pytz.UTC).isoformat(),
            "notified": notified,
            "notification_time": datetime.now(pytz.UTC).isoformat() if notification_sent else None
        }
        
        self.history_data["clusters"].append(cluster_record)
        self.save_history()
        
        logger.info(f"添加新集群: {cluster_key}, 通知状态: {notified}")
        return True
    
    def should_notify(self, cluster: Dict[str, Any]) -> bool:
        """判断是否应该发送通知"""
        cluster_key = self.get_cluster_key(cluster)

        # 如果已存在，不发送通知
        if self.cluster_exists(cluster_key):
            logger.debug(f"集群 {cluster_key} 已存在，跳过通知")
            return False

        # 检查集群是否稳定（避免对正在增长的集群发送通知）
        if not self.is_cluster_stable(cluster):
            logger.debug(f"集群 {cluster_key} 尚未稳定，暂不通知")
            return False

        # 检查是否在24小时内
        cluster_end_time = cluster['end_time']
        within_24h = self.is_within_24_hours(cluster_end_time)
        logger.info(f"集群 {cluster_key} 通知判断: 稳定=True, 24小时内={within_24h}")

        return within_24h
    
    def get_all_clusters(self, force_reload: bool = False) -> List[Dict[str, Any]]:
        """获取所有集群记录"""
        if force_reload:
            logger.info("强制重新加载历史数据")
            self.history_data = self.load_history()

        clusters = self.history_data["clusters"]
        logger.debug(f"获取所有集群记录: {len(clusters)} 个")
        return clusters

    def refresh_from_file(self) -> bool:
        """从文件刷新历史数据"""
        try:
            old_count = len(self.history_data["clusters"])
            self.history_data = self.load_history()
            new_count = len(self.history_data["clusters"])

            logger.info(f"历史数据刷新完成: {old_count} -> {new_count} 个集群")
            return True
        except Exception as e:
            logger.error(f"刷新历史数据失败: {e}")
            return False
    
    def get_notification_stats(self) -> Dict[str, int]:
        """获取通知状态统计"""
        clusters = self.history_data["clusters"]
        total = len(clusters)
        notified = sum(1 for c in clusters if c.get("notified", False))
        pending = total - notified
        
        return {
            "total_clusters": total,
            "notified_count": notified,
            "pending_count": pending
        }
    
    def cleanup_old_records(self, days: int = 30):
        """清理超过指定天数的旧记录"""
        cutoff_time = datetime.now(pytz.UTC) - timedelta(days=days)
        original_count = len(self.history_data["clusters"])
        
        self.history_data["clusters"] = [
            cluster for cluster in self.history_data["clusters"]
            if datetime.fromisoformat(cluster["first_detected"].replace('Z', '+00:00')) > cutoff_time
        ]
        
        removed_count = original_count - len(self.history_data["clusters"])
        if removed_count > 0:
            self.save_history()
            logger.info(f"清理了 {removed_count} 个超过 {days} 天的旧记录")
