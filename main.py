# main.py
import time
import signal
import sys
import os
from pathlib import Path
from threading import Event
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger

from core_config import settings
from business_analyzer import cluster_analyzer
from service_notification import notification_service
from core_database import clickhouse_client
from web_app import app
from core_logger import setup_logger

logger = setup_logger("main")

# 全局变量
scheduler = None
shutdown_event = Event()


def ensure_directories():
    """确保必要的目录存在"""
    directories = [
        "logs",      # 日志目录
        "data",      # 数据存储目录
    ]

    for directory in directories:
        dir_path = Path(directory)
        if not dir_path.exists():
            try:
                dir_path.mkdir(parents=True, exist_ok=True)
                logger.info(f"创建目录: {directory}")
            except Exception as e:
                logger.error(f"创建目录 {directory} 失败: {str(e)}")
                raise
        else:
            logger.debug(f"目录已存在: {directory}")


def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"接收到信号 {signum}，开始优雅关闭...")
    shutdown_event.set()


def scheduled_check():
    """定期执行的检查函数"""
    try:
        logger.debug("开始定期检查")
        
        # 获取集群数据
        clusters_df, _ = cluster_analyzer.get_clusters_data()

        if not clusters_df.empty:
            # 检查活跃集群并发送通知
            from web_app import check_and_notify
            check_and_notify(clusters_df)
        
        logger.debug("定期检查完成")
        
    except Exception as e:
        logger.error(f"定期检查失败: {str(e)}")


def start_scheduler():
    """启动调度器"""
    global scheduler
    
    try:
        scheduler = BackgroundScheduler()
        scheduler.add_job(
            func=scheduled_check,
            trigger=IntervalTrigger(seconds=settings.CHECK_INTERVAL),
            id='monitoring_job',
            name='Monitor liquidation clusters',
            replace_existing=True
        )
        scheduler.start()
        logger.info(f"调度器已启动，检查间隔: {settings.CHECK_INTERVAL}秒")
        
    except Exception as e:
        logger.error(f"启动调度器失败: {str(e)}")
        raise


def stop_scheduler():
    """停止调度器"""
    global scheduler
    
    if scheduler:
        try:
            scheduler.shutdown(wait=True)
            logger.info("调度器已停止")
        except Exception as e:
            logger.error(f"停止调度器时出错: {str(e)}")


def check_system_health():
    """检查系统健康状态"""
    try:
        # 检查数据库连接
        if not clickhouse_client.check_health():
            logger.warning("数据库连接不健康，尝试重新连接...")
            clickhouse_client.connect()
        
        return True
        
    except Exception as e:
        logger.error(f"系统健康检查失败: {str(e)}")
        return False


def run_web_server():
    """运行Web服务器"""
    try:
        logger.info(f"启动Web服务器: {settings.WEB_HOST}:{settings.WEB_PORT}")
        app.run(
            host=settings.WEB_HOST,
            port=settings.WEB_PORT,
            debug=settings.WEB_DEBUG,
            use_reloader=False,  # 避免重复启动
            threaded=True
        )
    except Exception as e:
        logger.error(f"Web服务器运行错误: {str(e)}")
        raise


def main():
    """主函数"""
    logger.info("=" * 50)
    logger.info("清算集群监控系统启动")
    logger.info("=" * 50)

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        # 确保必要的目录存在
        logger.info("初始化目录结构...")
        ensure_directories()

        # 检查系统健康状态
        logger.info("检查系统健康状态...")
        if not check_system_health():
            logger.error("系统健康检查失败，退出程序")
            return 1
        
        # 发送启动通知
        logger.info("发送启动通知...")
        if notification_service.send_test_notification():
            logger.info("启动通知发送成功")
        else:
            logger.warning("启动通知发送失败，但程序继续运行")
        
        # 启动调度器
        logger.info("启动监控调度器...")
        start_scheduler()
        
        # 运行Web服务器（阻塞）
        logger.info("启动Web服务器...")
        run_web_server()
        
    except KeyboardInterrupt:
        logger.info("接收到键盘中断信号")
    except Exception as e:
        logger.error(f"程序运行时发生严重错误: {str(e)}")
        return 1
    finally:
        # 清理资源
        logger.info("开始清理资源...")
        
        stop_scheduler()
        
        # 断开数据库连接
        try:
            clickhouse_client.disconnect()
        except Exception as e:
            logger.error(f"断开数据库连接时出错: {str(e)}")
        
        logger.info("程序已退出")
    
    return 0


def run_with_restart():
    """带重启功能的运行函数"""
    restart_count = 0
    max_restarts = 5
    restart_delay = 180  # 3分钟
    
    while restart_count < max_restarts:
        try:
            logger.info(f"程序启动 (重启次数: {restart_count})")
            exit_code = main()
            
            if exit_code == 0:
                logger.info("程序正常退出")
                break
            else:
                logger.error(f"程序异常退出，退出码: {exit_code}")
                
        except Exception as e:
            logger.error(f"程序运行时发生未捕获的异常: {str(e)}")
        
        restart_count += 1
        
        if restart_count < max_restarts:
            logger.info(f"等待 {restart_delay} 秒后重启... (剩余重启次数: {max_restarts - restart_count})")
            time.sleep(restart_delay)
        else:
            logger.error("达到最大重启次数，程序退出")
            return 1
    
    return 0


if __name__ == '__main__':
    # 根据配置决定是否启用自动重启
    if settings.WEB_DEBUG:
        # 调试模式下不自动重启
        sys.exit(main())
    else:
        # 生产模式下启用自动重启
        sys.exit(run_with_restart())
