# logger_02.py - 日志系统 (第二步：日志记录初始化)
import logging
import sys
import pytz
from datetime import datetime
from pathlib import Path


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        reset = self.COLORS['RESET']

        # 转换为UTC+8时间显示
        utc_time = datetime.fromtimestamp(record.created, tz=pytz.UTC)
        china_time = utc_time.astimezone(pytz.timezone('Asia/Shanghai'))
        record.asctime = china_time.strftime('%Y-%m-%d %H:%M:%S')

        log_message = f"{color}[{record.levelname}]{reset} {record.asctime} - {record.name} - {record.getMessage()}"

        if record.exc_info:
            log_message += f"\n{self.formatException(record.exc_info)}"

        return log_message


def setup_logger(name: str = "liquidation_monitor", level: str = "INFO") -> logging.Logger:
    """设置日志记录器"""
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))
    
    if logger.handlers:
        return logger
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.DEBUG)
    console_formatter = ColoredFormatter()
    console_handler.setFormatter(console_formatter)
    
    # 文件处理器
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # 使用UTC+8时间创建日志文件名
    china_time = datetime.now(pytz.timezone('Asia/Shanghai'))
    file_handler = logging.FileHandler(
        log_dir / f"{name}_{china_time.strftime('%Y%m%d')}.log",
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)

    # 创建自定义文件格式化器，显示UTC+8时间
    class ChinaTimeFormatter(logging.Formatter):
        def formatTime(self, record, datefmt=None):
            utc_time = datetime.fromtimestamp(record.created, tz=pytz.UTC)
            china_time = utc_time.astimezone(pytz.timezone('Asia/Shanghai'))
            return china_time.strftime('%Y-%m-%d %H:%M:%S')

    file_formatter = ChinaTimeFormatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    file_handler.setFormatter(file_formatter)
    
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)
    
    return logger
