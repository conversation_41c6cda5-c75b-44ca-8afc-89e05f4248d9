# service_notification.py - 通知服务 (外部服务：消息通知发送)
import requests
import urllib.parse
import time
from typing import Dict, Optional, Any
from datetime import datetime
import pytz

from core_config import settings
from core_logger import setup_logger

logger = setup_logger("notification")


class NotificationService:
    """通知服务 - 增强版本，支持重试和统计"""

    def __init__(self):
        self.retry_count = 3
        self.retry_delay = 2  # 秒
        self.notification_stats = {
            'total_sent': 0,
            'success_count': 0,
            'failure_count': 0,
            'last_success_time': None,
            'last_failure_time': None
        }
        logger.info("通知服务已初始化")
    
    def convert_to_china_timezone(self, utc_time: datetime) -> str:
        """转换UTC时间到中国时区(UTC+8)"""
        china = pytz.timezone('Asia/Shanghai')
        return utc_time.astimezone(china).strftime('%Y-%m-%d %H:%M:%S')

    def format_duration(self, seconds: float) -> str:
        """格式化持续时间为简洁格式 (例如: 8'11")"""
        if seconds <= 0:
            return '0"'

        if seconds < 60:
            return f'{seconds:.0f}"'

        minutes = int(seconds // 60)
        remaining_seconds = int(seconds % 60)

        if minutes < 60:
            if remaining_seconds == 0:
                return f"{minutes}'"
            else:
                return f'{minutes}\'{remaining_seconds}"'
        else:
            hours = int(minutes // 60)
            remaining_minutes = int(minutes % 60)

            if remaining_minutes == 0 and remaining_seconds == 0:
                return f"{hours}h"
            elif remaining_minutes == 0:
                return f'{hours}h{remaining_seconds}"'
            elif remaining_seconds == 0:
                return f"{hours}h{remaining_minutes}'"
            else:
                return f'{hours}h{remaining_minutes}\'{remaining_seconds}"'
    
    def send_bark_notification(self, title: str, content: str) -> bool:
        """发送Bark通知 - 增强版本，支持重试"""
        if not settings.ENABLE_BARK or not settings.BARK_URLS:
            logger.warning("Bark通知未启用或未配置URL")
            return False

        self.notification_stats['total_sent'] += 1
        success = False

        for url in settings.BARK_URLS:
            url_success = self._send_bark_with_retry(url, title, content)
            if url_success:
                success = True

        # 更新统计信息
        if success:
            self.notification_stats['success_count'] += 1
            self.notification_stats['last_success_time'] = datetime.now(pytz.UTC).isoformat()
        else:
            self.notification_stats['failure_count'] += 1
            self.notification_stats['last_failure_time'] = datetime.now(pytz.UTC).isoformat()

        return success

    def _send_bark_with_retry(self, url: str, title: str, content: str) -> bool:
        """带重试机制的Bark通知发送"""
        for attempt in range(self.retry_count):
            try:
                # URL编码处理中文字符
                encoded_title = urllib.parse.quote(title, safe='')
                encoded_content = urllib.parse.quote(content, safe='')

                # 构造通知URL
                notification_url = f"{url}/{encoded_title}/{encoded_content}"

                # 添加参数
                params = {
                    'call': '1',
                    'level': 'critical',
                    'group': '集群监控'
                }

                logger.debug(f"发送Bark通知到: {url} (尝试 {attempt + 1}/{self.retry_count})")

                response = requests.get(notification_url, params=params, timeout=10)

                if response.status_code == 200:
                    logger.info(f"成功发送Bark通知到: {url}")
                    return True
                else:
                    logger.warning(f"Bark通知发送失败: {url}, HTTP {response.status_code} (尝试 {attempt + 1}/{self.retry_count})")
                    if attempt < self.retry_count - 1:
                        time.sleep(self.retry_delay)

            except requests.exceptions.Timeout:
                logger.warning(f"Bark通知发送超时: {url} (尝试 {attempt + 1}/{self.retry_count})")
                if attempt < self.retry_count - 1:
                    time.sleep(self.retry_delay)
            except requests.exceptions.ConnectionError:
                logger.warning(f"Bark通知连接失败: {url} (尝试 {attempt + 1}/{self.retry_count})")
                if attempt < self.retry_count - 1:
                    time.sleep(self.retry_delay)
            except Exception as e:
                logger.warning(f"发送Bark通知时发生错误: {url}, {str(e)} (尝试 {attempt + 1}/{self.retry_count})")
                if attempt < self.retry_count - 1:
                    time.sleep(self.retry_delay)

        logger.error(f"Bark通知发送最终失败: {url}")
        return False
    
    def send_webhook_notification(self, title: str, content: str, cluster_data: Optional[Dict] = None) -> bool:
        """发送Webhook通知"""
        if not settings.ENABLE_WEBHOOK or not settings.WEBHOOK_URL:
            logger.warning("Webhook通知未启用或未配置URL")
            return False
        
        try:
            payload = {
                'title': title,
                'content': content,
                'timestamp': datetime.now(pytz.UTC).isoformat(),
                'cluster_data': cluster_data
            }
            
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'LiquidationMonitor/1.0'
            }
            
            logger.debug(f"发送Webhook通知到: {settings.WEBHOOK_URL}")
            
            response = requests.post(
                settings.WEBHOOK_URL,
                json=payload,
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                logger.info("成功发送Webhook通知")
                return True
            else:
                logger.error(f"Webhook通知发送失败: HTTP {response.status_code}")
                logger.error(f"响应内容: {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"发送Webhook通知时发生错误: {str(e)}")
            return False
    
    def send_test_notification(self) -> bool:
        """发送测试通知"""
        title = "监控系统启动"
        content = "清算集群监控系统已成功启动，通知功能正常。"
        
        logger.info("发送测试通知")
        
        success = False
        
        if settings.ENABLE_BARK:
            if self.send_bark_notification(title, content):
                success = True
        
        if settings.ENABLE_WEBHOOK:
            if self.send_webhook_notification(title, content):
                success = True
        
        return success
    
    def send_cluster_notification(self, cluster: Dict[str, Any]) -> bool:
        """发送集群通知"""
        title = "发现符合条件的爆仓集群!"
        
        # 格式化持续时间
        duration_seconds = cluster['duration_seconds']
        formatted_duration = self.format_duration(duration_seconds)

        # 计算平均每秒爆仓
        liquidations_per_second = cluster['total_liquidations'] / duration_seconds if duration_seconds > 0 else 0

        content = f"""
开始时间UTC+8: {self.convert_to_china_timezone(cluster['start_time'])}
结束时间UTC+8: {self.convert_to_china_timezone(cluster['end_time'])}
持续时间: {formatted_duration}
事件数: {cluster['event_count']}
总爆仓数: {cluster['total_liquidations']:.0f}
平均每次爆仓: {cluster['avg_liquidations']:.2f}
平均每秒爆仓: {liquidations_per_second:.2f}
最大单次爆仓: {cluster['max_single_liquidation']:.0f}

• 此提醒在集群结束1分钟后发送
• 适合在集群结束后1-20分钟内进行操作"""
        
        logger.info(f"发送集群通知: 集群ID {cluster.get('cluster_id', 'N/A')}")
        
        success = False
        
        if settings.ENABLE_BARK:
            if self.send_bark_notification(title, content):
                success = True
        
        if settings.ENABLE_WEBHOOK:
            if self.send_webhook_notification(title, content, cluster):
                success = True
        
        return success

    def get_notification_stats(self) -> Dict[str, Any]:
        """获取通知统计信息"""
        return self.notification_stats.copy()

    def reset_notification_stats(self) -> None:
        """重置通知统计信息"""
        self.notification_stats = {
            'total_sent': 0,
            'success_count': 0,
            'failure_count': 0,
            'last_success_time': None,
            'last_failure_time': None
        }
        logger.info("通知统计信息已重置")


# 全局通知服务实例
notification_service = NotificationService()
