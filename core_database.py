# core_database.py - 数据库连接管理 (核心基础：ClickHouse数据库连接)
import threading
from typing import Optional, List, Dict, Any
from clickhouse_driver import Client
from tenacity import retry, stop_after_attempt, wait_exponential
import pandas as pd
import pytz
from datetime import datetime, timedelta
import time
import queue
from contextlib import contextmanager

from core_config import settings
from core_logger import setup_logger

logger = setup_logger("database")


class ClickHouseConnectionPool:
    """ClickHouse连接池 - 解决并发查询问题"""

    def __init__(self, max_connections: int = 5):
        self.max_connections = max_connections
        self.pool = queue.Queue(maxsize=max_connections)
        self.active_connections = 0
        self.lock = threading.Lock()

        # 预创建一些连接
        self._initialize_pool()
        logger.info(f"ClickHouse连接池已初始化，最大连接数: {max_connections}")

    def _initialize_pool(self):
        """初始化连接池"""
        # 预创建2个连接
        for _ in range(min(2, self.max_connections)):
            try:
                conn = self._create_connection()
                if conn:
                    self.pool.put(conn)
                    self.active_connections += 1
            except Exception as e:
                logger.warning(f"预创建连接失败: {e}")

    def _create_connection(self) -> Optional[Client]:
        """创建新的ClickHouse连接"""
        try:
            client = Client(
                host=settings.CLICKHOUSE_HOST,
                port=settings.CLICKHOUSE_PORT,
                database=settings.CLICKHOUSE_DATABASE,
                user=settings.CLICKHOUSE_USER,
                password=settings.CLICKHOUSE_PASSWORD,
                settings=settings.CLICKHOUSE_SETTINGS
            )

            # 测试连接
            client.execute('SELECT 1')
            logger.debug("创建新的ClickHouse连接成功")
            return client

        except Exception as e:
            logger.error(f"创建ClickHouse连接失败: {str(e)}")
            return None

    @contextmanager
    def get_connection(self):
        """获取连接的上下文管理器"""
        connection = None
        try:
            # 尝试从池中获取连接
            try:
                connection = self.pool.get(timeout=5)  # 5秒超时
            except queue.Empty:
                # 池中没有可用连接，尝试创建新连接
                with self.lock:
                    if self.active_connections < self.max_connections:
                        connection = self._create_connection()
                        if connection:
                            self.active_connections += 1
                        else:
                            raise Exception("无法创建新的数据库连接")
                    else:
                        # 等待连接可用
                        connection = self.pool.get(timeout=10)  # 延长等待时间

            if not connection:
                raise Exception("无法获取数据库连接")

            # 检查连接是否有效
            try:
                connection.execute('SELECT 1')
            except Exception:
                # 连接无效，创建新连接
                logger.warning("检测到无效连接，重新创建")
                connection = self._create_connection()
                if not connection:
                    raise Exception("无法重新创建数据库连接")

            yield connection

        finally:
            # 归还连接到池中
            if connection:
                try:
                    self.pool.put(connection, timeout=1)
                except queue.Full:
                    # 池已满，关闭连接
                    try:
                        connection.disconnect()
                    except Exception:
                        pass
                    with self.lock:
                        self.active_connections -= 1

    def close_all(self):
        """关闭所有连接"""
        with self.lock:
            while not self.pool.empty():
                try:
                    conn = self.pool.get_nowait()
                    conn.disconnect()
                except (queue.Empty, Exception):
                    break
            self.active_connections = 0
        logger.info("所有连接已关闭")


class ClickHouseClient:
    """ClickHouse客户端管理器 - 使用连接池"""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        if self._initialized:
            return

        self.connection_pool = ClickHouseConnectionPool(max_connections=5)
        self._initialized = True
        logger.info("ClickHouse客户端管理器已初始化（使用连接池）")
    
    def disconnect(self):
        """断开所有连接"""
        try:
            self.connection_pool.close_all()
            logger.info("已断开所有ClickHouse连接")
        except Exception as e:
            logger.error(f"断开ClickHouse连接时出错: {str(e)}")

    def check_health(self) -> bool:
        """检查连接健康状态"""
        try:
            with self.connection_pool.get_connection() as client:
                client.execute('SELECT 1')
                return True
        except Exception as e:
            logger.warning(f"ClickHouse健康检查失败: {str(e)}")
            return False

    def execute_query(self, query: str, params: Optional[Dict] = None) -> List[Any]:
        """执行查询 - 使用连接池"""
        try:
            with self.connection_pool.get_connection() as client:
                logger.debug(f"执行查询: {query}")
                result = client.execute(query, params or {})
                logger.debug(f"查询返回 {len(result)} 行数据")
                return result
        except Exception as e:
            logger.error(f"查询执行失败: {str(e)}")
            raise
    
    def get_liquidation_data(self, hours: Optional[int] = None, start_time: Optional[datetime] = None, query_type: str = "auto") -> pd.DataFrame:
        """获取爆仓数据 - 支持增量查询优化"""
        start_query_time = time.time()

        # 确定查询时间范围
        if hours is None:
            hours = settings.QUERY_TIME_RANGE_HOURS

        # 构建查询条件
        if start_time:
            # 增量查询：从指定时间开始
            time_condition = f"event_time >= '{start_time.strftime('%Y-%m-%d %H:%M:%S')}'"
            logger.info(f"执行增量查询，起始时间: {start_time}")
        else:
            # 时间范围查询：查询指定小时数
            time_condition = f"event_time >= now() - INTERVAL {hours} HOUR"

            # 根据查询类型和时间范围判断日志描述
            if query_type == "initial" or hours >= 1000:  # 大于1000小时认为是全量查询
                logger.info(f"执行全量查询，时间范围: {hours} 小时")
            else:
                logger.info(f"执行增量查询，时间范围: {hours} 小时")

        query = f"""
        WITH
            toStartOfSecond(event_time) as time_sec,
            sum(if(side = 'BUY', 1, 0)) as long_liquidations,
            sum(if(side = 'SELL', 1, 0)) as short_liquidations
        SELECT
            time_sec,
            long_liquidations,
            short_liquidations
        FROM force_orders
        WHERE {time_condition}
        GROUP BY time_sec
        ORDER BY time_sec
        """

        try:
            result = self.execute_query(query)
            df = pd.DataFrame(result, columns=['timestamp', 'long_liquidations', 'short_liquidations'])

            # 确保时间戳有时区信息
            df['timestamp'] = df['timestamp'].apply(lambda x: x.replace(tzinfo=pytz.UTC))

            query_duration = time.time() - start_query_time
            logger.info(f"查询完成: {len(df)} 条记录, 耗时: {query_duration:.2f}秒")

            return df

        except Exception as e:
            logger.error(f"获取爆仓数据失败: {str(e)}")
            raise

    def get_liquidation_data_optimized(self, last_update_time: Optional[datetime] = None) -> pd.DataFrame:
        """优化的爆仓数据获取 - 智能增量查询"""
        current_time = datetime.now(pytz.UTC)

        if last_update_time is None:
            # 首次查询：使用全量查询时间范围
            initial_hours = getattr(settings, 'INITIAL_QUERY_RANGE_HOURS', 7200)
            logger.info(f"首次查询，获取最近 {initial_hours} 小时数据（全量查询）")
            return self.get_liquidation_data(hours=initial_hours, query_type="initial")
        else:
            # 增量查询：使用固定的增量查询时间范围
            time_diff = current_time - last_update_time
            incremental_hours = getattr(settings, 'QUERY_TIME_RANGE_HOURS', 48)

            if time_diff.total_seconds() > incremental_hours * 3600:
                # 长时间未更新，使用增量查询范围
                logger.info(f"长时间未更新({time_diff})，使用增量查询范围: {incremental_hours} 小时")
                return self.get_liquidation_data(hours=incremental_hours, query_type="incremental")
            else:
                # 短时间更新，使用增量查询范围确保数据完整性
                logger.info(f"常规增量查询，使用固定范围: {incremental_hours} 小时")
                return self.get_liquidation_data(hours=incremental_hours, query_type="incremental")

    def get_query_performance_stats(self) -> Dict[str, Any]:
        """获取查询性能统计"""
        try:
            # 查询数据库统计信息
            stats_query = """
            SELECT
                count() as total_records,
                min(event_time) as earliest_time,
                max(event_time) as latest_time,
                uniq(symbol) as unique_symbols
            FROM force_orders
            WHERE event_time >= now() - INTERVAL 24 HOUR
            """

            result = self.execute_query(stats_query)
            if result:
                stats = result[0]
                return {
                    'total_records_24h': stats[0],
                    'earliest_time': stats[1],
                    'latest_time': stats[2],
                    'unique_symbols': stats[3],
                    'query_time': datetime.now(pytz.UTC).isoformat()
                }
            else:
                return {}

        except Exception as e:
            logger.error(f"获取性能统计失败: {str(e)}")
            return {}


# 全局客户端实例
clickhouse_client = ClickHouseClient()
