# service_recovery.py - 错误处理和恢复服务
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, Any, List, Callable, Optional
import pytz
import traceback
from enum import Enum

from core_logger import setup_logger

logger = setup_logger("recovery")


class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class CircuitBreakerState(Enum):
    """断路器状态"""
    CLOSED = "closed"      # 正常状态
    OPEN = "open"          # 断开状态
    HALF_OPEN = "half_open"  # 半开状态


class CircuitBreaker:
    """断路器模式实现"""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60, expected_exception: type = Exception):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time = None
        self.state = CircuitBreakerState.CLOSED
        self.lock = threading.Lock()
    
    def call(self, func: Callable, *args, **kwargs):
        """执行函数调用，带断路器保护"""
        with self.lock:
            if self.state == CircuitBreakerState.OPEN:
                if self._should_attempt_reset():
                    self.state = CircuitBreakerState.HALF_OPEN
                    logger.info("断路器进入半开状态，尝试恢复")
                else:
                    raise Exception("断路器处于开启状态，拒绝执行")
            
            try:
                result = func(*args, **kwargs)
                self._on_success()
                return result
            except self.expected_exception as e:
                self._on_failure()
                raise e
    
    def _should_attempt_reset(self) -> bool:
        """判断是否应该尝试重置"""
        return (
            self.last_failure_time and
            datetime.now() - self.last_failure_time > timedelta(seconds=self.recovery_timeout)
        )
    
    def _on_success(self):
        """成功时的处理"""
        self.failure_count = 0
        self.state = CircuitBreakerState.CLOSED
        if self.state == CircuitBreakerState.HALF_OPEN:
            logger.info("断路器恢复到关闭状态")
    
    def _on_failure(self):
        """失败时的处理"""
        self.failure_count += 1
        self.last_failure_time = datetime.now()
        
        if self.failure_count >= self.failure_threshold:
            self.state = CircuitBreakerState.OPEN
            logger.warning(f"断路器开启，失败次数: {self.failure_count}")


class RetryManager:
    """重试管理器"""
    
    def __init__(self, max_retries: int = 3, base_delay: float = 1.0, max_delay: float = 60.0):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
    
    def retry_with_backoff(self, func: Callable, *args, **kwargs):
        """带指数退避的重试"""
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                
                if attempt == self.max_retries:
                    logger.error(f"重试失败，已达到最大重试次数 {self.max_retries}")
                    break
                
                delay = min(self.base_delay * (2 ** attempt), self.max_delay)
                logger.warning(f"第 {attempt + 1} 次尝试失败，{delay:.1f}秒后重试: {str(e)}")
                time.sleep(delay)
        
        raise last_exception


class ErrorRecoveryService:
    """错误恢复服务"""
    
    def __init__(self):
        self.error_history = []
        self.recovery_strategies = {}
        self.circuit_breakers = {}
        self.retry_manager = RetryManager()
        self.lock = threading.Lock()
        
        # 注册默认恢复策略
        self._register_default_strategies()
        logger.info("错误恢复服务已初始化")
    
    def _register_default_strategies(self):
        """注册默认恢复策略"""
        self.recovery_strategies.update({
            'database_connection': self._recover_database_connection,
            'notification_failure': self._recover_notification_service,
            'memory_pressure': self._recover_memory_pressure,
            'disk_space': self._recover_disk_space
        })
    
    def register_circuit_breaker(self, name: str, failure_threshold: int = 5, recovery_timeout: int = 60):
        """注册断路器"""
        self.circuit_breakers[name] = CircuitBreaker(
            failure_threshold=failure_threshold,
            recovery_timeout=recovery_timeout
        )
        logger.info(f"断路器已注册: {name}")
    
    def execute_with_protection(self, name: str, func: Callable, *args, **kwargs):
        """使用断路器保护执行函数"""
        if name not in self.circuit_breakers:
            self.register_circuit_breaker(name)
        
        return self.circuit_breakers[name].call(func, *args, **kwargs)
    
    def record_error(self, error_type: str, error_message: str, severity: ErrorSeverity = ErrorSeverity.MEDIUM, context: Optional[Dict] = None):
        """记录错误"""
        error_record = {
            'timestamp': datetime.now(pytz.UTC).isoformat(),
            'type': error_type,
            'message': error_message,
            'severity': severity.value,
            'context': context or {},
            'traceback': traceback.format_exc()
        }
        
        with self.lock:
            self.error_history.append(error_record)
            # 保持最近1000个错误记录
            self.error_history = self.error_history[-1000:]
        
        logger.error(f"错误记录: {error_type} - {error_message}")
        
        # 尝试自动恢复
        self._attempt_recovery(error_type, error_record)
    
    def _attempt_recovery(self, error_type: str, error_record: Dict):
        """尝试自动恢复"""
        if error_type in self.recovery_strategies:
            try:
                logger.info(f"尝试自动恢复: {error_type}")
                recovery_func = self.recovery_strategies[error_type]
                success = recovery_func(error_record)
                
                if success:
                    logger.info(f"自动恢复成功: {error_type}")
                else:
                    logger.warning(f"自动恢复失败: {error_type}")
                    
            except Exception as e:
                logger.error(f"恢复策略执行失败: {error_type}, {str(e)}")
    
    def _recover_database_connection(self, error_record: Dict) -> bool:
        """数据库连接恢复策略"""
        try:
            from core_database import clickhouse_client
            
            # 尝试重新连接
            if clickhouse_client.check_health():
                logger.info("数据库连接恢复成功")
                return True
            else:
                logger.warning("数据库连接恢复失败")
                return False
                
        except Exception as e:
            logger.error(f"数据库恢复策略失败: {e}")
            return False
    
    def _recover_notification_service(self, error_record: Dict) -> bool:
        """通知服务恢复策略"""
        try:
            from service_notification import notification_service
            
            # 发送测试通知验证服务状态
            success = notification_service.send_test_notification()
            if success:
                logger.info("通知服务恢复成功")
                return True
            else:
                logger.warning("通知服务恢复失败")
                return False
                
        except Exception as e:
            logger.error(f"通知服务恢复策略失败: {e}")
            return False
    
    def _recover_memory_pressure(self, error_record: Dict) -> bool:
        """内存压力恢复策略"""
        try:
            import gc
            
            # 强制垃圾回收
            gc.collect()
            logger.info("执行垃圾回收以释放内存")
            return True
            
        except Exception as e:
            logger.error(f"内存恢复策略失败: {e}")
            return False
    
    def _recover_disk_space(self, error_record: Dict) -> bool:
        """磁盘空间恢复策略"""
        try:
            # 清理临时文件和日志
            logger.info("尝试清理磁盘空间")
            # 这里可以添加具体的清理逻辑
            return True
            
        except Exception as e:
            logger.error(f"磁盘空间恢复策略失败: {e}")
            return False
    
    def get_error_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """获取错误统计信息"""
        cutoff_time = datetime.now(pytz.UTC) - timedelta(hours=hours)
        
        with self.lock:
            recent_errors = [
                e for e in self.error_history
                if datetime.fromisoformat(e['timestamp'].replace('Z', '+00:00')) > cutoff_time
            ]
        
        # 按类型统计
        error_counts = {}
        severity_counts = {}
        
        for error in recent_errors:
            error_type = error['type']
            severity = error['severity']
            
            error_counts[error_type] = error_counts.get(error_type, 0) + 1
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
        
        return {
            'total_errors': len(recent_errors),
            'error_types': error_counts,
            'severity_distribution': severity_counts,
            'recent_errors': recent_errors[-10:],  # 最近10个错误
            'circuit_breaker_status': {
                name: cb.state.value for name, cb in self.circuit_breakers.items()
            }
        }
    
    def reset_circuit_breaker(self, name: str) -> bool:
        """重置断路器"""
        if name in self.circuit_breakers:
            self.circuit_breakers[name].failure_count = 0
            self.circuit_breakers[name].state = CircuitBreakerState.CLOSED
            logger.info(f"断路器已重置: {name}")
            return True
        return False


# 全局错误恢复服务实例
error_recovery_service = ErrorRecoveryService()
