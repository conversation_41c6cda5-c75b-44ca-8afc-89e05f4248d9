#!/usr/bin/env python3
"""
测试ClickHouse连接池的并发性能
"""

import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from core_database import clickhouse_client
from core_logger import setup_logger

logger = setup_logger("test_connection_pool")


def test_concurrent_queries(thread_id: int, num_queries: int = 5):
    """测试并发查询"""
    results = []
    
    for i in range(num_queries):
        try:
            start_time = time.time()
            
            # 执行简单的测试查询
            result = clickhouse_client.execute_query("SELECT 1 as test_value")
            
            duration = time.time() - start_time
            results.append({
                'thread_id': thread_id,
                'query_id': i + 1,
                'duration': duration,
                'success': True,
                'result': result[0][0] if result else None
            })
            
            logger.info(f"线程 {thread_id} 查询 {i+1} 完成，耗时: {duration:.3f}秒")
            
            # 短暂休息
            time.sleep(0.1)
            
        except Exception as e:
            results.append({
                'thread_id': thread_id,
                'query_id': i + 1,
                'duration': 0,
                'success': False,
                'error': str(e)
            })
            logger.error(f"线程 {thread_id} 查询 {i+1} 失败: {e}")
    
    return results


def test_health_check():
    """测试健康检查"""
    try:
        is_healthy = clickhouse_client.check_health()
        logger.info(f"数据库健康检查: {'通过' if is_healthy else '失败'}")
        return is_healthy
    except Exception as e:
        logger.error(f"健康检查异常: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("开始测试ClickHouse连接池")
    
    # 1. 测试健康检查
    logger.info("=== 测试健康检查 ===")
    if not test_health_check():
        logger.error("数据库连接失败，停止测试")
        return
    
    # 2. 测试并发查询
    logger.info("=== 测试并发查询 ===")
    num_threads = 3
    queries_per_thread = 3
    
    start_time = time.time()
    
    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        # 提交任务
        futures = [
            executor.submit(test_concurrent_queries, thread_id, queries_per_thread)
            for thread_id in range(1, num_threads + 1)
        ]
        
        # 收集结果
        all_results = []
        for future in as_completed(futures):
            try:
                results = future.result()
                all_results.extend(results)
            except Exception as e:
                logger.error(f"线程执行异常: {e}")
    
    total_time = time.time() - start_time
    
    # 3. 分析结果
    logger.info("=== 测试结果分析 ===")
    successful_queries = [r for r in all_results if r['success']]
    failed_queries = [r for r in all_results if not r['success']]
    
    logger.info(f"总查询数: {len(all_results)}")
    logger.info(f"成功查询: {len(successful_queries)}")
    logger.info(f"失败查询: {len(failed_queries)}")
    logger.info(f"总耗时: {total_time:.3f}秒")
    
    if successful_queries:
        avg_duration = sum(r['duration'] for r in successful_queries) / len(successful_queries)
        logger.info(f"平均查询耗时: {avg_duration:.3f}秒")
    
    # 显示失败的查询
    if failed_queries:
        logger.warning("失败的查询:")
        for query in failed_queries:
            logger.warning(f"  线程 {query['thread_id']} 查询 {query['query_id']}: {query.get('error', '未知错误')}")
    
    # 4. 最终健康检查
    logger.info("=== 最终健康检查 ===")
    final_health = test_health_check()
    
    logger.info(f"测试完成，连接池状态: {'正常' if final_health else '异常'}")


if __name__ == "__main__":
    main()
