# web_app.py - Web应用 (Flask Web服务)
import os
from flask import Flask, send_file, jsonify, request
from threading import Lock
import pandas as pd
from datetime import datetime
import pytz

from core_config import settings
from business_analyzer import cluster_analyzer
from service_notification import notification_service
from business_history import ClusterHistoryManager
from service_monitoring import system_monitor
from service_recovery import error_recovery_service, ErrorSeverity
from core_logger import setup_logger

logger = setup_logger("web_app")

# 创建Flask应用
app = Flask(__name__)

# 数据锁和缓存
data_lock = Lock()
cluster_history = ClusterHistoryManager()  # 使用历史管理器替代简单缓存

# 启动系统监控
system_monitor.start_monitoring(interval_seconds=60)

# 注册关键断路器
error_recovery_service.register_circuit_breaker('cluster_data_fetch', failure_threshold=3, recovery_timeout=30)
error_recovery_service.register_circuit_breaker('database_query', failure_threshold=5, recovery_timeout=60)
error_recovery_service.register_circuit_breaker('notification_send', failure_threshold=3, recovery_timeout=120)


def convert_to_china_timezone(utc_time: datetime) -> str:
    """转换UTC时间到中国时区(UTC+8)"""
    china = pytz.timezone('Asia/Shanghai')
    return utc_time.astimezone(china).strftime('%Y-%m-%d %H:%M:%S')


def get_china_timezone():
    """获取中国时区对象"""
    return pytz.timezone('Asia/Shanghai')


def format_china_time(utc_time: datetime) -> str:
    """格式化UTC时间为中国时区字符串（用于日志显示）"""
    china_time = utc_time.astimezone(get_china_timezone())
    return china_time.strftime('%Y-%m-%d %H:%M:%S')


def get_api_timestamp() -> dict:
    """获取API响应用的时间戳（包含UTC和本地时间）"""
    utc_time = datetime.now(pytz.UTC)
    china_time = utc_time.astimezone(get_china_timezone())

    return {
        'timestamp_utc': utc_time.isoformat(),
        'timestamp_local': china_time.strftime('%Y-%m-%d %H:%M:%S'),
        'timezone': 'UTC+8'
    }


def format_duration(seconds: float) -> str:
    """格式化持续时间为简洁格式 (例如: 8'<sup>11"</sup>)"""
    if seconds <= 0:
        return '<sup>0"</sup>'

    if seconds < 60:
        return f'<sup>{seconds:.0f}"</sup>'

    minutes = int(seconds // 60)
    remaining_seconds = int(seconds % 60)

    if minutes < 60:
        if remaining_seconds == 0:
            return f"{minutes}'"
        else:
            return f'{minutes}\'<sup>{remaining_seconds}"</sup>'
    else:
        hours = int(minutes // 60)
        remaining_minutes = int(minutes % 60)

        if remaining_minutes == 0 and remaining_seconds == 0:
            return f"{hours}h"
        elif remaining_minutes == 0:
            return f'{hours}h<sup>{remaining_seconds}"</sup>'
        elif remaining_seconds == 0:
            return f"{hours}h{remaining_minutes}'"
        else:
            return f'{hours}h{remaining_minutes}\'<sup>{remaining_seconds}"</sup>'


def calculate_liquidations_per_second(total_liquidations: float, duration_seconds: float) -> float:
    """计算平均每秒爆仓数"""
    if duration_seconds <= 0:
        return 0.0
    return total_liquidations / duration_seconds


def check_and_notify(clusters_df: pd.DataFrame) -> None:
    """检查并发送通知 - 基于24小时时间窗口和集群稳定性"""
    if clusters_df.empty:
        logger.debug("没有检测到集群数据")
        return

    logger.info(f"开始检查 {len(clusters_df)} 个集群的通知状态")

    for _, cluster in clusters_df.iterrows():
        cluster_dict = cluster.to_dict()
        cluster_key = cluster_history.get_cluster_key(cluster_dict)

        # 记录集群基本信息
        logger.debug(f"检查集群: {cluster_key}")
        logger.debug(f"  - 开始时间: {cluster_dict['start_time']}")
        logger.debug(f"  - 结束时间: {cluster_dict['end_time']}")
        logger.debug(f"  - 事件数: {cluster_dict['event_count']}")

        # 判断是否应该发送通知（24小时内、稳定且未发送过）
        should_notify = cluster_history.should_notify(cluster_dict)

        if should_notify:
            logger.info(f"🔔 检测到需要通知的新集群: {cluster_key}")
            logger.info(f"   集群详情: 事件数={cluster_dict['event_count']}, 总爆仓={cluster_dict['total_liquidations']:.0f}")

            # 发送通知
            if notification_service.send_cluster_notification(cluster_dict):
                # 发送成功后添加到历史记录，标记为已通知且实际发送了通知
                cluster_history.add_cluster(cluster_dict, notified=True, notification_sent=True)
                logger.info(f"✅ 通知发送成功: {cluster_key}")
            else:
                # 发送失败也添加到历史记录，但标记为未通知
                cluster_history.add_cluster(cluster_dict, notified=False, notification_sent=False)
                logger.error(f"❌ 通知发送失败: {cluster_key}")
        else:
            # 不需要通知的集群也添加到历史记录（如果不存在）
            # 对于历史查询得到的集群，标记为已通知状态，避免重复发送通知，但不设置通知时间
            if not cluster_history.cluster_exists(cluster_key):
                cluster_history.add_cluster(cluster_dict, notified=True, notification_sent=False)
                logger.debug(f"📝 添加历史集群（标记为已通知）: {cluster_key}")
            else:
                logger.debug(f"⏭️  集群已存在，跳过: {cluster_key}")

    logger.info("集群通知检查完成")


def generate_html_table(direction_str: str) -> str:
    """生成HTML表格 - 显示所有历史集群"""
    current_time = datetime.now(pytz.UTC)
    logger.debug(f"当前时间 (UTC+8): {format_china_time(current_time)}")

    # 从历史记录获取统计信息
    stats = cluster_history.get_notification_stats()
    total_clusters = stats['total_clusters']
    notified_count = stats['notified_count']
    pending_count = stats['pending_count']

    # 添加调试信息
    logger.info(f"生成HTML表格 - 统计信息: 总集群={total_clusters}, 已通知={notified_count}, 待通知={pending_count}")

    html = f"""
    <div class="stats">
        <h2>爆仓集群分析（{direction_str}）</h2>

        <div class="notification-stats" style="margin-bottom: 15px; padding: 10px; background: rgba(255,255,255,0.05); border-radius: 8px; border-left: 4px solid var(--primary-color);">
            <div style="display: flex; gap: 20px; align-items: center; font-size: 0.9rem;">
                <span style="color: var(--text-secondary);">通知状态统计：</span>
                <span style="color: var(--success-color); font-weight: bold;">
                    <span class="status-icon notified" style="display: inline-block; margin-right: 5px;">✓</span>
                    已发送: {notified_count}
                </span>
                <span style="color: var(--warning-color); font-weight: bold;">
                    <span class="status-icon pending" style="display: inline-block; margin-right: 5px;">○</span>
                    待发送: {pending_count}
                </span>
                <span style="color: var(--text-secondary);">总计: {total_clusters}</span>
            </div>
        </div>
        <table>
            <thead>
                <tr>
                    <th>通知状态</th>
                    <th>集群ID</th>
                    <th>开始时间UTC+8</th>
                    <th>结束时间UTC+8</th>
                    <th>持续时间</th>
                    <th>事件数</th>
                    <th>总爆仓数</th>
                    <th>平均每次爆仓</th>
                    <th>平均每秒爆仓</th>
                    <th>最大单次爆仓</th>
                </tr>
            </thead>
            <tbody>
    """

    # 从历史记录获取所有集群数据（按时间倒序）
    all_clusters = cluster_history.get_all_clusters()
    logger.info(f"从历史记录获取到 {len(all_clusters)} 个集群")

    if all_clusters:
        all_clusters.sort(key=lambda x: x['start_time'], reverse=True)
        logger.debug(f"集群时间范围: {all_clusters[-1]['start_time']} 到 {all_clusters[0]['start_time']}")
    else:
        logger.warning("历史记录中没有集群数据")

    for cluster_record in all_clusters:
        start_time = datetime.fromisoformat(cluster_record['start_time'].replace('Z', '+00:00'))
        end_time = datetime.fromisoformat(cluster_record['end_time'].replace('Z', '+00:00'))

        # 检查通知状态
        is_notified = cluster_record.get('notified', False)

        # 根据通知状态设置图标和样式
        if is_notified:
            status_icon = "✓"
            status_class = "notified"
            status_title = "已发送Bark通知"
        else:
            status_icon = "○"
            status_class = "pending"
            status_title = "等待发送通知"

        # 计算格式化的持续时间和平均每秒爆仓
        duration_seconds = cluster_record.get('duration_seconds', 0)
        total_liquidations = cluster_record.get('total_liquidations', 0)
        formatted_duration = format_duration(duration_seconds)
        liquidations_per_second = calculate_liquidations_per_second(total_liquidations, duration_seconds)

        html += f"""
            <tr>
                <td>
                    <span class="status-icon {status_class}" title="{status_title}">{status_icon}</span>
                </td>
                <td>{cluster_record.get('cluster_id', 'N/A')}</td>
                <td>{convert_to_china_timezone(start_time)}</td>
                <td>{convert_to_china_timezone(end_time)}</td>
                <td class="number">{formatted_duration}</td>
                <td class="number">{cluster_record.get('event_count', 0)}</td>
                <td class="number">{total_liquidations:.0f}</td>
                <td class="number">{cluster_record.get('avg_liquidations', 0):.2f}</td>
                <td class="number">{liquidations_per_second:.2f}</td>
                <td class="number">{cluster_record.get('max_single_liquidation', 0):.0f}</td>
            </tr>
        """

    html += """
            </tbody>
        </table>
    </div>
    """
    return html


@app.route('/')
def home():
    """主页"""
    try:
        # 获取项目根目录的index.html
        current_dir = os.path.dirname(os.path.abspath(__file__))
        index_path = os.path.join(current_dir, 'index.html')
        return send_file(index_path)
    except Exception as e:
        logger.error(f"发送主页文件失败: {str(e)}")
        return "页面加载失败", 500


@app.route('/get_data')
def get_data():
    """获取数据API - 增强错误处理"""
    with data_lock:
        try:
            logger.debug("开始获取集群数据")

            # 使用断路器保护数据获取
            def get_cluster_data():
                return cluster_analyzer.get_clusters_data()

            clusters_df, direction_str = error_recovery_service.execute_with_protection(
                'cluster_data_fetch', get_cluster_data
            )

            # 检查并发送通知
            check_and_notify(clusters_df)

            # 生成HTML表格
            html_content = generate_html_table(direction_str)

            logger.debug("数据获取完成")
            return html_content

        except Exception as e:
            # 记录错误到恢复服务
            error_recovery_service.record_error(
                error_type='data_fetch_failure',
                error_message=str(e),
                severity=ErrorSeverity.HIGH,
                context={'endpoint': '/get_data'}
            )

            logger.error(f"获取数据失败: {str(e)}")
            return "数据暂时不可用，系统将在稍后自动重试", 503


@app.route('/api/status')
def api_status():
    """系统状态API"""
    try:
        # 检查数据库连接
        from core_database import clickhouse_client
        db_healthy = clickhouse_client.check_health()
        
        status = {
            'status': 'healthy' if db_healthy else 'unhealthy',
            'database': 'connected' if db_healthy else 'disconnected',
            **get_api_timestamp(),
            'config': {
                'min_liquidations': settings.MIN_LIQUIDATIONS,
                'liq_direction': settings.LIQ_DIRECTION,
                'cluster_max_gap': settings.CLUSTER_MAX_GAP,
                'min_events': settings.MIN_EVENTS
            }
        }
        
        return jsonify(status)
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.now(pytz.UTC).isoformat()
        }), 500


@app.route('/api/test_notification')
def api_test_notification():
    """测试通知API"""
    try:
        success = notification_service.send_test_notification()
        
        return jsonify({
            'success': success,
            'message': '测试通知发送成功' if success else '测试通知发送失败',
            'timestamp': datetime.now(pytz.UTC).isoformat()
        })
        
    except Exception as e:
        logger.error(f"发送测试通知失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': str(e),
            'timestamp': datetime.now(pytz.UTC).isoformat()
        }), 500


@app.route('/api/notification_status')
def get_notification_status():
    """获取通知状态信息"""
    try:
        # 从历史记录获取所有集群
        all_clusters = cluster_history.get_all_clusters()

        if not all_clusters:
            return jsonify({
                'total_clusters': 0,
                'notified_count': 0,
                'pending_count': 0,
                'notified_clusters': [],
                'pending_clusters': []
            })

        notified_list = []
        pending_list = []

        for cluster_record in all_clusters:
            cluster_info = {
                'cluster_id': cluster_record.get('cluster_id'),
                'start_time': cluster_record['start_time'],
                'end_time': cluster_record['end_time'],
                'cluster_key': cluster_record['cluster_key']
            }

            if cluster_record.get('notified', False):
                notified_list.append(cluster_info)
            else:
                pending_list.append(cluster_info)

        return jsonify({
            'total_clusters': len(all_clusters),
            'notified_count': len(notified_list),
            'pending_count': len(pending_list),
            'notified_clusters': notified_list,
            'pending_clusters': pending_list
        })
    except Exception as e:
        logger.error(f"获取通知状态失败: {e}")
        return jsonify({
            'error': f'获取通知状态失败: {str(e)}'
        }), 500


@app.route('/api/debug')
def api_debug():
    """调试信息API"""
    try:
        # 获取实时集群数据
        clusters_df, direction_str = cluster_analyzer.get_clusters_data()

        # 获取历史记录数据
        all_clusters = cluster_history.get_all_clusters()

        # 获取性能统计
        performance_stats = cluster_analyzer.get_performance_stats()

        debug_info = {
            'timestamp': datetime.now(pytz.UTC).isoformat(),
            'realtime_clusters': {
                'count': len(clusters_df),
                'direction': direction_str,
                'clusters': clusters_df.to_dict('records') if not clusters_df.empty else []
            },
            'history_clusters': {
                'count': len(all_clusters),
                'clusters': all_clusters[:5]  # 只返回前5个用于调试
            },
            'performance': performance_stats,
            'config': {
                'min_liquidations': settings.MIN_LIQUIDATIONS,
                'liq_direction': settings.LIQ_DIRECTION,
                'cluster_max_gap': settings.CLUSTER_MAX_GAP,
                'min_events': settings.MIN_EVENTS,
                'query_time_range_hours': settings.QUERY_TIME_RANGE_HOURS
            }
        }

        return jsonify(debug_info)

    except Exception as e:
        logger.error(f"获取调试信息失败: {str(e)}")
        return jsonify({
            'error': str(e),
            'timestamp': datetime.now(pytz.UTC).isoformat()
        }), 500


@app.route('/api/refresh_history')
def api_refresh_history():
    """刷新历史数据API"""
    try:
        success = cluster_history.refresh_from_file()

        if success:
            all_clusters = cluster_history.get_all_clusters()
            return jsonify({
                'success': True,
                'message': f'历史数据刷新成功，当前有 {len(all_clusters)} 个集群',
                'cluster_count': len(all_clusters),
                'timestamp': datetime.now(pytz.UTC).isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'message': '历史数据刷新失败',
                'timestamp': datetime.now(pytz.UTC).isoformat()
            }), 500

    except Exception as e:
        logger.error(f"刷新历史数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now(pytz.UTC).isoformat()
        }), 500


@app.route('/api/notification_stats')
def api_notification_stats():
    """获取通知统计信息API"""
    try:
        stats = notification_service.get_notification_stats()

        return jsonify({
            'stats': stats,
            'timestamp': datetime.now(pytz.UTC).isoformat()
        })

    except Exception as e:
        logger.error(f"获取通知统计失败: {str(e)}")
        return jsonify({
            'error': str(e),
            'timestamp': datetime.now(pytz.UTC).isoformat()
        }), 500


@app.route('/api/reset_notification_stats', methods=['POST'])
def api_reset_notification_stats():
    """重置通知统计信息API"""
    try:
        notification_service.reset_notification_stats()

        return jsonify({
            'success': True,
            'message': '通知统计信息已重置',
            'timestamp': datetime.now(pytz.UTC).isoformat()
        })

    except Exception as e:
        logger.error(f"重置通知统计失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now(pytz.UTC).isoformat()
        }), 500


@app.route('/api/system_metrics')
def api_system_metrics():
    """获取系统指标API"""
    try:
        metrics = system_monitor.get_current_metrics()

        return jsonify({
            'metrics': metrics,
            'timestamp': datetime.now(pytz.UTC).isoformat()
        })

    except Exception as e:
        logger.error(f"获取系统指标失败: {str(e)}")
        return jsonify({
            'error': str(e),
            'timestamp': datetime.now(pytz.UTC).isoformat()
        }), 500


@app.route('/api/system_summary')
def api_system_summary():
    """获取系统摘要信息API"""
    try:
        summary = system_monitor.get_system_summary()

        return jsonify({
            'summary': summary,
            'timestamp': datetime.now(pytz.UTC).isoformat()
        })

    except Exception as e:
        logger.error(f"获取系统摘要失败: {str(e)}")
        return jsonify({
            'error': str(e),
            'timestamp': datetime.now(pytz.UTC).isoformat()
        }), 500


@app.route('/api/system_alerts')
def api_system_alerts():
    """获取系统告警API"""
    try:
        hours = int(request.args.get('hours', 24))
        alerts = system_monitor.get_alerts(hours=hours)

        return jsonify({
            'alerts': alerts,
            'count': len(alerts),
            'hours': hours,
            'timestamp': datetime.now(pytz.UTC).isoformat()
        })

    except Exception as e:
        logger.error(f"获取系统告警失败: {str(e)}")
        return jsonify({
            'error': str(e),
            'timestamp': datetime.now(pytz.UTC).isoformat()
        }), 500


@app.route('/api/error_statistics')
def api_error_statistics():
    """获取错误统计API"""
    try:
        hours = int(request.args.get('hours', 24))
        stats = error_recovery_service.get_error_statistics(hours=hours)

        return jsonify({
            'statistics': stats,
            'hours': hours,
            'timestamp': datetime.now(pytz.UTC).isoformat()
        })

    except Exception as e:
        logger.error(f"获取错误统计失败: {str(e)}")
        return jsonify({
            'error': str(e),
            'timestamp': datetime.now(pytz.UTC).isoformat()
        }), 500


@app.route('/api/reset_circuit_breaker/<name>', methods=['POST'])
def api_reset_circuit_breaker(name: str):
    """重置断路器API"""
    try:
        success = error_recovery_service.reset_circuit_breaker(name)

        if success:
            return jsonify({
                'success': True,
                'message': f'断路器 {name} 已重置',
                'timestamp': datetime.now(pytz.UTC).isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'message': f'断路器 {name} 不存在',
                'timestamp': datetime.now(pytz.UTC).isoformat()
            }), 404

    except Exception as e:
        logger.error(f"重置断路器失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now(pytz.UTC).isoformat()
        }), 500


@app.route('/api/health_check')
def api_health_check():
    """系统健康检查API"""
    try:
        health_status = {
            'timestamp': datetime.now(pytz.UTC).isoformat(),
            'status': 'healthy',
            'components': {}
        }

        # 检查数据库连接
        try:
            from core_database import clickhouse_client
            db_healthy = clickhouse_client.check_health()
            health_status['components']['database'] = 'healthy' if db_healthy else 'unhealthy'
        except Exception as e:
            health_status['components']['database'] = 'error'
            health_status['status'] = 'degraded'

        # 检查通知服务
        try:
            notification_stats = notification_service.get_notification_stats()
            health_status['components']['notification'] = 'healthy'
        except Exception as e:
            health_status['components']['notification'] = 'error'
            health_status['status'] = 'degraded'

        # 检查监控服务
        try:
            system_summary = system_monitor.get_system_summary()
            health_status['components']['monitoring'] = 'healthy' if system_summary['monitoring_active'] else 'inactive'
        except Exception as e:
            health_status['components']['monitoring'] = 'error'
            health_status['status'] = 'degraded'

        # 检查错误恢复服务
        try:
            error_stats = error_recovery_service.get_error_statistics(hours=1)
            critical_errors = sum(1 for e in error_stats['recent_errors'] if e['severity'] == 'critical')
            health_status['components']['error_recovery'] = 'healthy' if critical_errors == 0 else 'warning'
        except Exception as e:
            health_status['components']['error_recovery'] = 'error'
            health_status['status'] = 'degraded'

        # 确定整体状态
        if any(status == 'error' for status in health_status['components'].values()):
            health_status['status'] = 'unhealthy'
        elif any(status in ['unhealthy', 'warning'] for status in health_status['components'].values()):
            health_status['status'] = 'degraded'

        return jsonify(health_status)

    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e),
            'timestamp': datetime.now(pytz.UTC).isoformat()
        }), 500


@app.after_request
def add_security_headers(response):
    """添加安全headers"""
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    return response


@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Not found'}), 404


@app.errorhandler(500)
def internal_error(error):
    logger.error(f"内部服务器错误: {str(error)}")
    return jsonify({'error': 'Internal server error'}), 500


if __name__ == '__main__':
    app.run(
        host=settings.WEB_HOST,
        port=settings.WEB_PORT,
        debug=settings.WEB_DEBUG
    )
