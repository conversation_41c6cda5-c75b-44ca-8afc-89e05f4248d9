# ClickHouse数据库配置
# CLICKHOUSE_HOST=***********
CLICKHOUSE_HOST=localhost
CLICKHOUSE_DATABASE=mydatabase
CLICKHOUSE_USER=myuser
CLICKHOUSE_PASSWORD=mypassword
CLICKHOUSE_PORT=9000

# 监控配置
MIN_LIQUIDATIONS=120
LIQ_DIRECTION=SELL
CLUSTER_MAX_GAP=600
MIN_EVENTS=20
CHECK_INTERVAL=60

# 数据查询配置
QUERY_TIME_RANGE_HOURS=24
INITIAL_QUERY_RANGE_HOURS=7200

# 通知配置
# BARK_URLS=http://**************:8580/FhQbH8Fo7veS6cVAnbL8Mm
BARK_URLS=http://**************:8580/FhQbH8Fo7veS6cVAnbL8Mm,http://**************:8580/GdAQW76UyCciRMzkLZuCQZ
ENABLE_BARK=true
WEBHOOK_URL=
ENABLE_WEBHOOK=false

# Web服务配置
WEB_HOST=0.0.0.0
WEB_PORT=7002
WEB_DEBUG=false


