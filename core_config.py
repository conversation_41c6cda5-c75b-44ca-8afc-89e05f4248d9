# config_01.py - 配置管理 (第一步：系统配置加载)
import os
from typing import List, Dict, Any
from dataclasses import dataclass
from pathlib import Path


def load_env_file(env_path: str = ".env"):
    """加载.env文件中的环境变量"""
    env_file = Path(env_path)
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()


@dataclass
class Settings:
    """应用设置 - 从环境变量和.env文件加载"""
    
    # ClickHouse配置
    CLICKHOUSE_HOST: str = ""
    CLICKHOUSE_DATABASE: str = ""
    CLICKHOUSE_USER: str = ""
    CLICKHOUSE_PASSWORD: str = ""
    CLICKHOUSE_PORT: int = 9000
    CLICKHOUSE_SETTINGS: Dict[str, Any] = None
    
    # 监控配置
    MIN_LIQUIDATIONS: int = 120
    LIQ_DIRECTION: str = 'SELL'
    CLUSTER_MAX_GAP: int = 480
    MIN_EVENTS: int = 20
    CHECK_INTERVAL: int = 60
    
    # 数据查询配置
    QUERY_TIME_RANGE_HOURS: int = 48  # 增量查询时间范围（小时）
    INITIAL_QUERY_RANGE_HOURS: int = 7200  # 首次全量查询时间范围（小时，约300天）
    
    # 通知配置
    BARK_URLS: List[str] = None
    ENABLE_BARK: bool = True
    ENABLE_WEBHOOK: bool = False
    WEBHOOK_URL: str = ""
    
    # Web服务配置
    WEB_HOST: str = "0.0.0.0"
    WEB_PORT: int = 7002
    WEB_DEBUG: bool = False
    
    def __post_init__(self):
        # 首先加载.env文件
        load_env_file()
        
        # 设置默认值
        if self.CLICKHOUSE_SETTINGS is None:
            self.CLICKHOUSE_SETTINGS = {
                'max_threads': 8,
                'max_block_size': 100000,
                'max_parallel_replicas': 2
            }
        
        # 从环境变量加载配置
        self._load_from_env()
        
        # 验证必需的配置
        self._validate_config()
    
    def _load_from_env(self):
        """从环境变量加载配置"""
        # ClickHouse配置 - 必需的配置
        self.CLICKHOUSE_HOST = os.getenv('CLICKHOUSE_HOST', '***********')
        self.CLICKHOUSE_DATABASE = os.getenv('CLICKHOUSE_DATABASE', 'mydatabase')
        self.CLICKHOUSE_USER = os.getenv('CLICKHOUSE_USER', 'myuser')
        self.CLICKHOUSE_PASSWORD = os.getenv('CLICKHOUSE_PASSWORD', 'mypassword')
        self.CLICKHOUSE_PORT = int(os.getenv('CLICKHOUSE_PORT', '9000'))
        
        # 监控配置
        self.MIN_LIQUIDATIONS = int(os.getenv('MIN_LIQUIDATIONS', '120'))
        self.LIQ_DIRECTION = os.getenv('LIQ_DIRECTION', 'SELL')
        self.CLUSTER_MAX_GAP = int(os.getenv('CLUSTER_MAX_GAP', '480'))
        self.MIN_EVENTS = int(os.getenv('MIN_EVENTS', '20'))
        self.CHECK_INTERVAL = int(os.getenv('CHECK_INTERVAL', '60'))
        
        # 数据查询配置
        self.QUERY_TIME_RANGE_HOURS = int(os.getenv('QUERY_TIME_RANGE_HOURS', '48'))
        self.INITIAL_QUERY_RANGE_HOURS = int(os.getenv('INITIAL_QUERY_RANGE_HOURS', '7200'))
        
        # 通知配置
        bark_urls_env = os.getenv('BARK_URLS')
        if bark_urls_env:
            self.BARK_URLS = [url.strip() for url in bark_urls_env.split(',')]
        else:
            # 默认的Bark URLs
            self.BARK_URLS = [
                "http://**************:8580/5AMQrvxB6Cr4sK2T79EJzA",
                "http://**************:8580/rCVmKnnYE6suwG6qpvY2ye"
            ]
        
        self.ENABLE_BARK = os.getenv('ENABLE_BARK', 'true').lower() == 'true'
        self.WEBHOOK_URL = os.getenv('WEBHOOK_URL', '')
        self.ENABLE_WEBHOOK = os.getenv('ENABLE_WEBHOOK', 'false').lower() == 'true'
        
        # Web配置
        self.WEB_HOST = os.getenv('WEB_HOST', '0.0.0.0')
        self.WEB_PORT = int(os.getenv('WEB_PORT', '7002'))
        self.WEB_DEBUG = os.getenv('WEB_DEBUG', 'false').lower() == 'true'
    
    def _validate_config(self):
        """验证配置的有效性"""
        required_fields = [
            'CLICKHOUSE_HOST', 'CLICKHOUSE_DATABASE', 
            'CLICKHOUSE_USER', 'CLICKHOUSE_PASSWORD'
        ]
        
        for field in required_fields:
            if not getattr(self, field):
                raise ValueError(f"必需的配置项 {field} 未设置")
        
        print(f"✅ 配置加载完成:")
        print(f"   - ClickHouse: {self.CLICKHOUSE_HOST}:{self.CLICKHOUSE_PORT}/{self.CLICKHOUSE_DATABASE}")
        print(f"   - 监控方向: {self.LIQ_DIRECTION}")
        print(f"   - 增量查询范围: 最近 {self.QUERY_TIME_RANGE_HOURS} 小时")
        print(f"   - 全量查询范围: 最近 {self.INITIAL_QUERY_RANGE_HOURS} 小时")
        print(f"   - Bark通知: {'启用' if self.ENABLE_BARK else '禁用'} ({len(self.BARK_URLS)} 个URL)")
        print(f"   - Web服务: {self.WEB_HOST}:{self.WEB_PORT}")


# 全局设置实例
settings = Settings()
