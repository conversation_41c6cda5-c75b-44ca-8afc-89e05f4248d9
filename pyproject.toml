[project]
name = "liquidation-monitor"
version = "1.0.0"
description = "清算集群监控系统"
requires-python = ">=3.11"
dependencies = [
    "clickhouse-driver>=0.2.9",
    "pandas>=2.3.1",
    "pytz>=2025.2",
    "requests>=2.32.4",
    "flask>=3.1.1",
    "cachetools>=6.1.0",
    "apscheduler>=3.11.0",
    "tenacity>=9.1.2",
    "psutil>=7.0.0",
]

[tool.hatch.build.targets.wheel]
packages = ["."]


[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"


