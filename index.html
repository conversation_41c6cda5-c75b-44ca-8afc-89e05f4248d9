<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>清算集群监控系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap">
    <style>
        /* 字体优化设置 */
        * {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 针对不同操作系统的字体优化 */
        @media screen and (-webkit-min-device-pixel-ratio: 2) {
            body {
                font-weight: 400;
            }
        }

        /* 确保中文字符的正确显示 */
        :lang(zh-CN) {
            font-family: 'HarmonyOS Sans SC', 'Noto Sans SC', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei UI', 'Microsoft YaHei', sans-serif;
        }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
    <style>
        :root {
            --primary-color: #f0b90b;
            --primary-hover: #f8d12f;
            --secondary-color: #1e2329;
            --accent-color: #f0b90b;
            --success-color: #03a66d;
            --warning-color: #f0b90b;
            --danger-color: #cf304a;
            --bg-primary: #0b0e11;
            --bg-secondary: #161a1e;
            --bg-tertiary: #1e2329;
            --text-primary: #eaecef;
            --text-secondary: #848e9c;
            --text-tertiary: #474d57;
            --border-color: #2a2f37;
            --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            --hover-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
            --table-header-bg: #161a1e;
            --table-row-hover: #2b3139;
            --table-border: #2a2f37;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'HarmonyOS Sans SC', 'Noto Sans SC', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei UI', 'Microsoft YaHei', 'Source Han Sans SC', 'Source Han Sans CN', 'WenQuanYi Micro Hei', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            min-height: 100vh;
            font-feature-settings: 'kern' 1, 'liga' 1;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .navbar {
            background-color: var(--bg-primary);
            border-bottom: 1px solid var(--border-color);
            padding: 0.75rem 1.5rem;
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            gap: 10px;
            color: var(--text-primary);
            font-weight: 600;
            font-size: 1.2rem;
        }

        .navbar-brand img {
            height: 28px;
        }

        .navbar-nav .nav-link {
            color: var(--text-secondary);
            font-weight: 500;
            padding: 0.5rem 1rem;
            transition: color 0.2s;
        }

        .navbar-nav .nav-link:hover,
        .navbar-nav .nav-link.active {
            color: var(--primary-color);
        }

        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header-card {
            background-color: var(--bg-secondary);
            border-radius: 4px;
            border: 1px solid var(--border-color);
            padding: 16px 20px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title {
            display: flex;
            flex-direction: column;
        }

        .header-title h1 {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .header-subtitle {
            font-size: 0.85rem;
            color: var(--text-secondary);
            margin-top: 5px;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: var(--secondary-color);
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: 500;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.2s;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
            transform: translateY(-1px);
        }

        .btn-primary:active {
            transform: translateY(0);
        }

        .btn-secondary {
            background-color: var(--bg-tertiary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: 500;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.2s;
        }

        .btn-secondary:hover {
            background-color: var(--bg-secondary);
            border-color: var(--primary-color);
            color: var(--primary-color);
            transform: translateY(-1px);
        }

        .btn-secondary:active {
            transform: translateY(0);
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.85rem;
            color: var(--text-secondary);
            padding: 4px 8px;
            border-radius: 4px;
            background-color: rgba(132, 142, 156, 0.1);
        }

        .status-indicator i {
            font-size: 14px;
            color: var(--success-color);
        }

        .content-card {
            background-color: var(--bg-secondary);
            border-radius: 4px;
            border: 1px solid var(--border-color);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .card-header {
            padding: 16px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .card-title i {
            color: var(--primary-color);
            font-size: 1rem;
        }

        .card-body {
            padding: 0;
        }

        .data-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }

        .data-table th {
            background-color: var(--table-header-bg);
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding: 12px 16px;
            text-align: right;
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .data-table td {
            padding: 14px 16px;
            text-align: right;
            border-bottom: 1px solid var(--border-color);
            font-size: 0.9rem;
            color: var(--text-primary);
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }

        .data-table tr:hover td {
            background-color: var(--table-row-hover);
        }

        .data-table th:first-child,
        .data-table td:first-child {
            text-align: center;
        }

        .number {
            font-family: 'HarmonyOS Sans SC', 'Noto Sans SC', 'SF Mono', Monaco, 'Fira Code', 'JetBrains Mono', Consolas, 'Liberation Mono', 'Courier New', monospace;
            font-weight: 500;
            font-variant-numeric: tabular-nums;
            letter-spacing: 0.02em;
            font-feature-settings: 'tnum' 1, 'zero' 1;
        }

        .status-cell {
            width: 60px;
        }

        .status-icon {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            margin: 0 auto;
            font-size: 12px;
        }

        .status-icon.notified {
            background-color: rgba(3, 166, 109, 0.15);
            color: var(--success-color);
            border: 2px solid var(--success-color);
            font-weight: bold;
            animation: pulse-success 2s infinite;
        }

        .status-icon.pending {
            background-color: rgba(240, 185, 11, 0.15);
            color: var(--warning-color);
            border: 2px solid var(--warning-color);
            font-weight: bold;
            animation: pulse-warning 2s infinite;
        }

        @keyframes pulse-success {
            0% { box-shadow: 0 0 0 0 rgba(3, 166, 109, 0.4); }
            70% { box-shadow: 0 0 0 6px rgba(3, 166, 109, 0); }
            100% { box-shadow: 0 0 0 0 rgba(3, 166, 109, 0); }
        }

        @keyframes pulse-warning {
            0% { box-shadow: 0 0 0 0 rgba(240, 185, 11, 0.4); }
            70% { box-shadow: 0 0 0 6px rgba(240, 185, 11, 0); }
            100% { box-shadow: 0 0 0 0 rgba(240, 185, 11, 0); }
        }

        .cluster-id {
            display: inline-block;
            background-color: rgba(240, 185, 11, 0.1);
            color: var(--primary-color);
            padding: 2px 8px;
            border-radius: 4px;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .time-tag {
            display: inline-block;
            padding: 2px 6px;
            background-color: rgba(132, 142, 156, 0.1);
            color: var(--text-primary);
            border-radius: 3px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .highlight-value {
            color: var(--primary-color);
        }

        .warning-value {
            color: var(--warning-color);
        }

        .danger-value {
            color: var(--danger-color);
        }

        .loading-spinner {
            display: inline-block;
            width: 18px;
            height: 18px;
            border: 2px solid rgba(240, 185, 11, 0.2);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .error-message {
            padding: 16px;
            text-align: center;
            background-color: rgba(207, 48, 74, 0.1);
            color: var(--danger-color);
            border-radius: 4px;
            margin: 20px;
            border: 1px solid rgba(207, 48, 74, 0.2);
            font-size: 0.9rem;
        }

        .dashboard-footer {
            text-align: center;
            padding: 15px;
            color: var(--text-secondary);
            font-size: 0.8rem;
            margin-top: 20px;
            border-top: 1px solid var(--border-color);
        }

        /* 响应式设计 */
        @media (max-width: 992px) {
            .header-card {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }
            
            .header-actions {
                width: 100%;
                justify-content: space-between;
            }
            
            .data-table {
                display: block;
                overflow-x: auto;
                white-space: nowrap;
            }
        }

        /* 数据标签 */
        .data-label {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 0.75rem;
            color: var(--text-secondary);
            margin-left: 8px;
        }

        /* 工具提示 */
        .tooltip-wrapper {
            position: relative;
            display: inline-block;
        }

        .tooltip-wrapper:hover .tooltip-content {
            visibility: visible;
            opacity: 1;
        }

        .tooltip-content {
            visibility: hidden;
            opacity: 0;
            position: absolute;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            background-color: var(--bg-tertiary);
            color: var(--text-primary);
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 0.8rem;
            white-space: nowrap;
            z-index: 100;
            box-shadow: var(--card-shadow);
            border: 1px solid var(--border-color);
            transition: opacity 0.2s, visibility 0.2s;
        }

        .tooltip-content::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: var(--border-color) transparent transparent transparent;
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-primary);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--text-tertiary);
        }

        /* Solid.js 风格元素 */
        .solid-card {
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .solid-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--hover-shadow);
        }

        .solid-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0 8px;
            height: 20px;
            border-radius: 10px;
            font-size: 0.75rem;
            font-weight: 600;
            background-color: rgba(240, 185, 11, 0.15);
            color: var(--primary-color);
            margin-left: 8px;
        }

        .cluster-info {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }

        .cluster-info-icon {
            color: var(--primary-color);
            font-size: 1.2rem;
        }

        .cluster-info-text {
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .table-header-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .table-header-title {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 后台提供的标题样式 */
        .backend-title {
            font-size: 0.85rem; /* 字体调小2号 */
        }

        .table-header-actions {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .table-header-info {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .table-header-info i {
            color: var(--primary-color);
            font-size: 0.9rem;
        }

        .table-filter {
            background-color: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 0.8rem;
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.2s;
        }

        .table-filter:hover {
            background-color: var(--bg-secondary);
        }

        .check-column {
            width: 40px;
        }

        .check-icon {
            color: var(--success-color);
            font-size: 1rem;
        }

        .last-update-badge {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 6px 10px;
            border-radius: 4px;
            background-color: rgba(3, 166, 109, 0.1);
            color: var(--success-color);
            font-size: 0.85rem;
            font-weight: 500;
        }

        .last-update-badge i {
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <!-- 导航栏内容已移除 -->
        </div>
    </nav>

    <div class="dashboard-container">
        <!-- 头部信息卡片 -->
        <div class="header-card solid-card">
            <div class="header-title">
                <h1>
                    <i class="bi bi-lightning-charge-fill" style="color: var(--primary-color);"></i>
                    清算集群监控系统
                    <span class="solid-badge">实时</span>
                </h1>
                <div class="header-subtitle">适合在集群结束后1-20分钟内进行操作 · 数据每分钟自动更新</div>
            </div>
            <div class="header-actions">
                <div class="last-update-badge">
                    <i class="bi bi-clock-history"></i>
                    <span>最后更新: <span id="last-update">2025/03/14 21:08:40</span></span>
                </div>
                <button id="test-notification" class="btn btn-secondary me-2">
                    <i class="bi bi-bell"></i>
                    测试通知
                </button>
                <button id="refresh" class="btn btn-primary">
                    <i class="bi bi-arrow-repeat"></i>
                    立即刷新
                </button>
            </div>
        </div>
        
        <!-- 内容卡片 -->
        <div id="content" class="content-card solid-card">
            <div class="table-header-section">
                <div class="table-header-title">
                    <i class="bi bi-table"></i>
                    <span class="backend-title"></span>
                    <span id="time-diff-info" style="font-size: 0.85rem; font-weight: normal; margin-left: 10px; color: var(--text-secondary);"></span>
                </div>
                <div class="table-header-actions">
                    <div class="table-header-info">
                        <i class="bi bi-info-circle"></i>
                        <!-- 时间差信息已移至标题区域 -->
                    </div>
                    <div class="table-filter">
                        <i class="bi bi-funnel"></i>
                        筛选
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- 数据将在这里插入 -->
                <div class="d-flex justify-content-center py-5">
                    <div class="loading-spinner"></div>
                    <span class="ms-2" style="color: var(--text-secondary);">正在加载数据...</span>
                </div>
            </div>
        </div>
        
        <div class="dashboard-footer">
            &copy; 2025 清算集群监控系统 | 版本 1.2.0 | 数据实时更新
        </div>
    </div>

    <script>
        let isRefreshing = false;
        
        function updateLastRefreshTime() {
            const now = new Date();
            const formattedDate = now.getFullYear() + '/' + 
                                 String(now.getMonth() + 1).padStart(2, '0') + '/' + 
                                 String(now.getDate()).padStart(2, '0') + ' ' + 
                                 String(now.getHours()).padStart(2, '0') + ':' + 
                                 String(now.getMinutes()).padStart(2, '0') + ':' + 
                                 String(now.getSeconds()).padStart(2, '0');
            $('#last-update').text(formattedDate);
        }

        function showError(message) {
            $('#content .card-body').html(`<div class="error-message">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>${message}
            </div>`);
        }

        function enhanceTableData() {
            // 增强表格数据的可视化效果
            setTimeout(() => {
                // 为表格添加类
                $('table').addClass('data-table');
                
                // 为集群ID添加样式
                $('table td:nth-child(2)').each(function() {
                    const text = $(this).text().trim();
                    if (text) {
                        $(this).html(`<span class="cluster-id">${text}</span>`);
                    }
                });
                
                // 为数值添加高亮样式
                $('td.number').each(function() {
                    const value = parseFloat($(this).text());
                    if (value > 100) {
                        $(this).addClass('danger-value');
                    } else if (value > 50) {
                        $(this).addClass('warning-value');
                    } else if (value > 20) {
                        $(this).addClass('highlight-value');
                    }
                });
                
                // 为时间添加样式
                $('table td:nth-child(3), table td:nth-child(4)').each(function() {
                    const text = $(this).text().trim();
                    if (text) {
                        $(this).html(`<span class="time-tag">${text}</span>`);
                    }
                });
                
                // 为状态列添加样式
                $('table td:first-child').addClass('status-cell');
                
                // 计算最后一条数据的时间差
                if ($('table tr').length > 1) {
                    const lastEndTimeStr = $('table tr:eq(1) td:nth-child(4)').text().trim();
                    if (lastEndTimeStr) {
                        // 解析最后一条数据的结束时间
                        const lastEndTime = moment(lastEndTimeStr, 'YYYY-MM-DD HH:mm:ss');
                        const now = moment();
                        const diffMinutes = now.diff(lastEndTime, 'minutes');
                        
                        // 显示时间差信息
                        let timeInfo = '';
                        if (diffMinutes < 60) {
                            timeInfo = `最近一次爆仓发生在 ${diffMinutes} 分钟前`;
                        } else if (diffMinutes < 24 * 60) {
                            const hours = Math.floor(diffMinutes / 60);
                            const mins = diffMinutes % 60;
                            timeInfo = `最近一次爆仓发生在 ${hours} 小时 ${mins} 分钟前`;
                        } else {
                            const days = Math.floor(diffMinutes / (24 * 60));
                            const hours = Math.floor((diffMinutes % (24 * 60)) / 60);
                            timeInfo = `最近一次爆仓发生在 ${days} 天 ${hours} 小时前`;
                        }
                        
                        $('#time-diff-info').text(timeInfo);
                    }
                }
            }, 100);
        }

        function refreshData() {
            if (isRefreshing) return;
            
            isRefreshing = true;
            $('#refresh').prop('disabled', true);
            $('#refresh').html('<span class="loading-spinner"></span> 刷新中...');
            
            $.ajax({
                url: '/get_data',
                method: 'GET',
                success: function(response) {
                    // 提取标题
                    let titleText = '';
                    const titleMatch = response.match(/<h2[^>]*>(.*?)<\/h2>/);
                    if (titleMatch && titleMatch[1]) {
                        titleText = titleMatch[1];
                    }
                    
                    // 移除响应中的h2标题，避免重复显示
                    const cleanedResponse = response.replace(/<h2[^>]*>.*?<\/h2>/g, '');
                    
                    // 设置标题到指定位置
                    $('.backend-title').text(titleText);
                    
                    // 更新内容区域，不包含标题
                    $('#content .card-body').html(cleanedResponse);
                    
                    updateLastRefreshTime();
                    enhanceTableData();
                },
                error: function(xhr, status, error) {
                    console.error('获取数据失败:', error);
                    showError('获取数据失败。请稍后再试。');
                },
                complete: function() {
                    isRefreshing = false;
                    $('#refresh').prop('disabled', false);
                    $('#refresh').html('<i class="bi bi-arrow-repeat"></i> 立即刷新');
                }
            });
        }

        // 初始加载
        refreshData();

        // 设置每分钟自动刷新
        setInterval(refreshData, 60000);

        // 手动刷新按钮
        $('#refresh').click(refreshData);

        // 测试通知按钮
        $('#test-notification').click(function() {
            if ($(this).prop('disabled')) return;

            $(this).prop('disabled', true);
            $(this).html('<span class="loading-spinner"></span> 发送中...');

            $.ajax({
                url: '/api/test_notification',
                method: 'GET',
                success: function(response) {
                    if (response.success) {
                        alert('测试通知发送成功！请检查您的通知设备。');
                    } else {
                        alert('测试通知发送失败：' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('测试通知失败:', error);
                    alert('测试通知发送失败，请检查网络连接。');
                },
                complete: function() {
                    $('#test-notification').prop('disabled', false);
                    $('#test-notification').html('<i class="bi bi-bell"></i> 测试通知');
                }
            });
        });
    </script>
</body>
</html>