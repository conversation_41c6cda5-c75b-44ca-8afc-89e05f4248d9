# business_analyzer.py - 集群分析服务 (业务逻辑：爆仓集群分析)
import pandas as pd
import time
import pytz
from typing import List, Dict, Any, Tuple
from datetime import datetime

from core_config import settings
from core_database import clickhouse_client
from core_logger import setup_logger

logger = setup_logger("cluster_analyzer")


class ClusterAnalyzer:
    """集群分析服务 - 优化版本"""

    def __init__(self):
        self.last_update_time = None
        self.performance_stats = {}
        logger.info("集群分析服务已初始化")
    
    def cluster_events(self, events_df: pd.DataFrame, max_gap_seconds: int) -> List[List[int]]:
        """聚类事件 - 优化版本"""
        if events_df.empty:
            logger.debug("事件数据为空，返回空集群列表")
            return []

        start_time = time.time()
        clusters = []
        current_cluster = [0]

        # 预计算时间戳数组以提高性能
        timestamps = events_df['timestamp'].values

        for i in range(1, len(timestamps)):
            time_diff = timestamps[i] - timestamps[i-1]
            # 处理 numpy.timedelta64 对象
            if hasattr(time_diff, 'total_seconds'):
                time_gap = time_diff.total_seconds()
            else:
                # 对于 numpy.timedelta64，转换为秒
                time_gap = time_diff / pd.Timedelta(seconds=1)

            if time_gap <= max_gap_seconds:
                current_cluster.append(i)
            else:
                if len(current_cluster) >= settings.MIN_EVENTS:  # 提前过滤小集群
                    clusters.append(current_cluster)
                current_cluster = [i]

        # 处理最后一个集群
        if len(current_cluster) >= settings.MIN_EVENTS:
            clusters.append(current_cluster)

        processing_time = time.time() - start_time
        logger.debug(f"聚类完成: {len(clusters)} 个有效集群, 耗时: {processing_time:.3f}秒")

        return clusters

    def cluster_events_advanced(self, events_df: pd.DataFrame, max_gap_seconds: int) -> List[List[int]]:
        """高级聚类算法 - 支持动态间隔调整"""
        if events_df.empty:
            return []

        start_time = time.time()
        clusters = []
        current_cluster = [0]

        timestamps = events_df['timestamp'].values
        liquidations = events_df[self._get_liquidation_column()].values

        for i in range(1, len(timestamps)):
            time_diff = timestamps[i] - timestamps[i-1]
            # 处理 numpy.timedelta64 对象
            if hasattr(time_diff, 'total_seconds'):
                time_gap = time_diff.total_seconds()
            else:
                # 对于 numpy.timedelta64，转换为秒
                time_gap = time_diff / pd.Timedelta(seconds=1)

            # 动态调整间隔：高强度期间允许更大间隔
            current_intensity = liquidations[i]
            adaptive_gap = max_gap_seconds

            if current_intensity > settings.MIN_LIQUIDATIONS * 2:
                adaptive_gap = max_gap_seconds * 1.5  # 高强度时放宽间隔

            if time_gap <= adaptive_gap:
                current_cluster.append(i)
            else:
                if len(current_cluster) >= settings.MIN_EVENTS:
                    clusters.append(current_cluster)
                current_cluster = [i]

        if len(current_cluster) >= settings.MIN_EVENTS:
            clusters.append(current_cluster)

        processing_time = time.time() - start_time
        logger.info(f"高级聚类完成: {len(clusters)} 个集群, 耗时: {processing_time:.3f}秒")

        return clusters

    def _get_liquidation_column(self) -> str:
        """获取清算方向对应的列名"""
        return 'short_liquidations' if settings.LIQ_DIRECTION == 'SELL' else 'long_liquidations'
    
    def analyze_clusters(self, clusters: List[List[int]], threshold_exceeded: pd.DataFrame, liquidation_col: str) -> List[Dict]:
        """分析集群数据"""
        cluster_stats = []
        
        for i, cluster_indices in enumerate(clusters):
            cluster_data = threshold_exceeded.iloc[cluster_indices]
            
            # 计算持续时间
            time_diff = cluster_data['timestamp'].max() - cluster_data['timestamp'].min()
            if hasattr(time_diff, 'total_seconds'):
                duration_seconds = time_diff.total_seconds()
            else:
                # 对于 numpy.timedelta64，转换为秒
                duration_seconds = time_diff / pd.Timedelta(seconds=1)

            stats = {
                'cluster_id': i + 1,
                'start_time': cluster_data['timestamp'].min(),
                'end_time': cluster_data['timestamp'].max(),
                'duration_seconds': duration_seconds,
                'event_count': len(cluster_data),
                'total_liquidations': cluster_data[liquidation_col].sum(),
                'avg_liquidations': cluster_data[liquidation_col].sum() / len(cluster_data),
                'max_single_liquidation': cluster_data[liquidation_col].max()
            }
            
            cluster_stats.append(stats)
        
        logger.debug(f"分析完成 {len(cluster_stats)} 个集群")
        return cluster_stats
    
    def get_clusters_data(self, use_cache: bool = True) -> Tuple[pd.DataFrame, str]:
        """获取集群数据 - 优化版本"""
        start_time = time.time()

        try:
            # 使用优化的数据库查询
            df = clickhouse_client.get_liquidation_data_optimized(self.last_update_time)

            if df.empty:
                logger.warning("未获取到爆仓数据")
                return pd.DataFrame(), "无数据"

            # 更新最后查询时间（使用UTC时区）
            self.last_update_time = datetime.now(pytz.UTC)

            # 根据配置确定监控方向
            direction_str, liquidation_col = self._prepare_liquidation_data(df)

            # 筛选超过阈值的事件
            threshold_exceeded = df[df['exceeds_threshold']].copy()

            if threshold_exceeded.empty:
                logger.info("未发现超过阈值的爆仓事件")
                return pd.DataFrame(), direction_str

            logger.info(f"发现 {len(threshold_exceeded)} 个超过阈值的爆仓事件")

            # 使用优化的聚类算法
            clusters = self.cluster_events_advanced(threshold_exceeded, settings.CLUSTER_MAX_GAP)
            cluster_stats = self.analyze_clusters(clusters, threshold_exceeded, liquidation_col)

            # 转换为DataFrame并筛选
            clusters_df = pd.DataFrame(cluster_stats)
            if not clusters_df.empty:
                clusters_df = clusters_df.sort_values('start_time', ascending=False)
                # 已在聚类阶段过滤，这里不需要再次过滤

            processing_time = time.time() - start_time
            self.performance_stats = {
                'last_processing_time': processing_time,
                'total_events': len(df),
                'threshold_events': len(threshold_exceeded),
                'final_clusters': len(clusters_df),
                'timestamp': datetime.now(pytz.UTC).isoformat()
            }

            logger.info(f"集群分析完成: {len(clusters_df)} 个集群, 总耗时: {processing_time:.2f}秒")

            return clusters_df, direction_str

        except Exception as e:
            logger.error(f"获取集群数据失败: {str(e)}")
            raise

    def _prepare_liquidation_data(self, df: pd.DataFrame) -> Tuple[str, str]:
        """准备清算数据和方向信息"""
        if settings.LIQ_DIRECTION == 'SELL':
            df['exceeds_threshold'] = df['short_liquidations'] > settings.MIN_LIQUIDATIONS
            direction_str = "多单爆仓"
            liquidation_col = 'short_liquidations'
        elif settings.LIQ_DIRECTION == 'BUY':
            df['exceeds_threshold'] = df['long_liquidations'] > settings.MIN_LIQUIDATIONS
            direction_str = "空单爆仓"
            liquidation_col = 'long_liquidations'
        else:  # BOTH
            df['exceeds_threshold'] = (
                (df['long_liquidations'] > settings.MIN_LIQUIDATIONS) |
                (df['short_liquidations'] > settings.MIN_LIQUIDATIONS)
            )
            direction_str = "多空双爆"
            df['total_liquidations'] = df['long_liquidations'] + df['short_liquidations']
            liquidation_col = 'total_liquidations'

        return direction_str, liquidation_col

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        db_stats = clickhouse_client.get_query_performance_stats()
        return {
            'analyzer_stats': self.performance_stats,
            'database_stats': db_stats
        }
    

    def get_cluster_key(self, cluster: Dict[str, Any]) -> str:
        """生成集群的唯一标识 - 使用稳定的时间和事件特征"""
        start_time_str = cluster['start_time'].strftime('%Y-%m-%d %H:%M:%S')
        end_time_str = cluster['end_time'].strftime('%Y-%m-%d %H:%M:%S')
        event_count = cluster.get('event_count', 0)
        total_liquidations = int(cluster.get('total_liquidations', 0))

        # 使用时间范围 + 事件数量 + 总爆仓量作为唯一标识
        # 这样避免了cluster_id的不稳定性问题
        return f"{start_time_str}_{end_time_str}_{event_count}_{total_liquidations}"


# 全局集群分析器实例
cluster_analyzer = ClusterAnalyzer()
