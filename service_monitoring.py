# service_monitoring.py - 系统监控服务
import psutil
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import pytz
import threading
import json
import os

from core_config import settings
from core_logger import setup_logger

logger = setup_logger("monitoring")


class SystemMonitor:
    """系统监控服务 - 监控系统性能和健康状态"""
    
    def __init__(self):
        self.monitoring_data = {
            'system_metrics': [],
            'performance_history': [],
            'alerts': [],
            'start_time': datetime.now(pytz.UTC).isoformat()
        }
        self.monitoring_lock = threading.Lock()
        self.monitoring_active = False
        self.monitoring_thread = None
        self.alert_thresholds = {
            'cpu_percent': 80.0,
            'memory_percent': 85.0,
            'disk_percent': 90.0,
            'response_time_ms': 5000
        }
        logger.info("系统监控服务已初始化")
    
    def start_monitoring(self, interval_seconds: int = 60) -> None:
        """启动系统监控"""
        if self.monitoring_active:
            logger.warning("监控已在运行中")
            return
        
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            args=(interval_seconds,),
            daemon=True
        )
        self.monitoring_thread.start()
        logger.info(f"系统监控已启动，监控间隔: {interval_seconds}秒")
    
    def stop_monitoring(self) -> None:
        """停止系统监控"""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        logger.info("系统监控已停止")
    
    def _monitoring_loop(self, interval_seconds: int) -> None:
        """监控循环"""
        while self.monitoring_active:
            try:
                metrics = self.collect_system_metrics()
                self._store_metrics(metrics)
                self._check_alerts(metrics)
                time.sleep(interval_seconds)
            except Exception as e:
                logger.error(f"监控循环错误: {e}")
                time.sleep(interval_seconds)
    
    def collect_system_metrics(self) -> Dict[str, Any]:
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            
            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            
            # 网络IO
            net_io = psutil.net_io_counters()
            
            # 进程信息
            process = psutil.Process()
            process_memory = process.memory_info()
            
            metrics = {
                'timestamp': datetime.now(pytz.UTC).isoformat(),
                'cpu': {
                    'percent': cpu_percent,
                    'count': psutil.cpu_count()
                },
                'memory': {
                    'total': memory.total,
                    'available': memory.available,
                    'percent': memory.percent,
                    'used': memory.used
                },
                'disk': {
                    'total': disk.total,
                    'used': disk.used,
                    'free': disk.free,
                    'percent': (disk.used / disk.total) * 100
                },
                'network': {
                    'bytes_sent': net_io.bytes_sent,
                    'bytes_recv': net_io.bytes_recv,
                    'packets_sent': net_io.packets_sent,
                    'packets_recv': net_io.packets_recv
                },
                'process': {
                    'memory_rss': process_memory.rss,
                    'memory_vms': process_memory.vms,
                    'cpu_percent': process.cpu_percent()
                }
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"收集系统指标失败: {e}")
            return {}
    
    def _store_metrics(self, metrics: Dict[str, Any]) -> None:
        """存储指标数据"""
        if not metrics:
            return
        
        with self.monitoring_lock:
            self.monitoring_data['system_metrics'].append(metrics)
            
            # 保持最近24小时的数据
            cutoff_time = datetime.now(pytz.UTC) - timedelta(hours=24)
            self.monitoring_data['system_metrics'] = [
                m for m in self.monitoring_data['system_metrics']
                if datetime.fromisoformat(m['timestamp'].replace('Z', '+00:00')) > cutoff_time
            ]
    
    def _check_alerts(self, metrics: Dict[str, Any]) -> None:
        """检查告警条件"""
        if not metrics:
            return
        
        alerts = []
        
        # CPU告警
        if metrics['cpu']['percent'] > self.alert_thresholds['cpu_percent']:
            alerts.append({
                'type': 'cpu_high',
                'message': f"CPU使用率过高: {metrics['cpu']['percent']:.1f}%",
                'severity': 'warning',
                'value': metrics['cpu']['percent'],
                'threshold': self.alert_thresholds['cpu_percent']
            })
        
        # 内存告警
        if metrics['memory']['percent'] > self.alert_thresholds['memory_percent']:
            alerts.append({
                'type': 'memory_high',
                'message': f"内存使用率过高: {metrics['memory']['percent']:.1f}%",
                'severity': 'warning',
                'value': metrics['memory']['percent'],
                'threshold': self.alert_thresholds['memory_percent']
            })
        
        # 磁盘告警
        if metrics['disk']['percent'] > self.alert_thresholds['disk_percent']:
            alerts.append({
                'type': 'disk_high',
                'message': f"磁盘使用率过高: {metrics['disk']['percent']:.1f}%",
                'severity': 'critical',
                'value': metrics['disk']['percent'],
                'threshold': self.alert_thresholds['disk_percent']
            })
        
        # 存储告警
        if alerts:
            with self.monitoring_lock:
                for alert in alerts:
                    alert['timestamp'] = datetime.now(pytz.UTC).isoformat()
                    self.monitoring_data['alerts'].append(alert)
                    logger.warning(f"系统告警: {alert['message']}")
                
                # 保持最近100个告警
                self.monitoring_data['alerts'] = self.monitoring_data['alerts'][-100:]
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """获取当前系统指标"""
        return self.collect_system_metrics()
    
    def get_metrics_history(self, hours: int = 1) -> List[Dict[str, Any]]:
        """获取指标历史数据"""
        cutoff_time = datetime.now(pytz.UTC) - timedelta(hours=hours)
        
        with self.monitoring_lock:
            return [
                m for m in self.monitoring_data['system_metrics']
                if datetime.fromisoformat(m['timestamp'].replace('Z', '+00:00')) > cutoff_time
            ]
    
    def get_alerts(self, hours: int = 24) -> List[Dict[str, Any]]:
        """获取告警历史"""
        cutoff_time = datetime.now(pytz.UTC) - timedelta(hours=hours)
        
        with self.monitoring_lock:
            return [
                a for a in self.monitoring_data['alerts']
                if datetime.fromisoformat(a['timestamp'].replace('Z', '+00:00')) > cutoff_time
            ]
    
    def get_system_summary(self) -> Dict[str, Any]:
        """获取系统摘要信息"""
        current_metrics = self.get_current_metrics()
        recent_alerts = self.get_alerts(hours=1)
        
        return {
            'current_metrics': current_metrics,
            'recent_alerts': recent_alerts,
            'alert_count_24h': len(self.get_alerts(hours=24)),
            'monitoring_active': self.monitoring_active,
            'uptime_hours': (
                datetime.now(pytz.UTC) - 
                datetime.fromisoformat(self.monitoring_data['start_time'].replace('Z', '+00:00'))
            ).total_seconds() / 3600
        }
    
    def update_alert_thresholds(self, thresholds: Dict[str, float]) -> None:
        """更新告警阈值"""
        self.alert_thresholds.update(thresholds)
        logger.info(f"告警阈值已更新: {self.alert_thresholds}")


# 全局监控服务实例
system_monitor = SystemMonitor()
